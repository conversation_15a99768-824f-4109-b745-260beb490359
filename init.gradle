allprojects {
    repositories {
        def ALIYUN_REPOSITORY_URL = 'https://maven.aliyun.com/repository/public'
        def ALIYUN_GRADLE_PLUGIN_URL = 'https://maven.aliyun.com/repository/gradle-plugin'
        def ALIYUN_GOOGLE_URL = 'https://maven.aliyun.com/repository/google'
        def ALIYUN_JCENTER_URL = 'https://maven.aliyun.com/repository/jcenter'

        all { ArtifactRepository repo ->
            if (repo instanceof MavenArtifactRepository) {
                def url = repo.url.toString()
                if (url.startsWith('https://repo.maven.apache.org/maven2')) {
                    project.logger.lifecycle "Repository ${repo.url} replaced by $ALIYUN_REPOSITORY_URL."
                    remove repo
                }
                if (url.startsWith('https://plugins.gradle.org/m2')) {
                    project.logger.lifecycle "Repository ${repo.url} replaced by $ALIYUN_GRADLE_PLUGIN_URL."
                    remove repo
                }
                if (url.startsWith('https://dl.google.com/dl/android/maven2')) {
                    project.logger.lifecycle "Repository ${repo.url} replaced by $ALIYUN_GOOGLE_URL."
                    remove repo
                }
                if (url.startsWith('https://jcenter.bintray.com')) {
                    project.logger.lifecycle "Repository ${repo.url} replaced by $ALIYUN_JCENTER_URL."
                    remove repo
                }
                if (url.startsWith('https://services.gradle.org/distributions')) {
                    project.logger.lifecycle "Repository ${repo.url} replaced by https://mirrors.aliyun.com/gradle/distributions."
                    remove repo
                }
            }
        }

        maven { url ALIYUN_REPOSITORY_URL }
        maven { url ALIYUN_GRADLE_PLUGIN_URL }
        maven { url ALIYUN_GOOGLE_URL }
        maven { url ALIYUN_JCENTER_URL }
    }
}

// Configure Gradle wrapper download location
gradle.projectsLoaded {
    rootProject.allprojects {
        buildscript {
            configurations.all {
                resolutionStrategy.eachDependency { DependencyResolveDetails details ->
                    if (details.requested.group == 'org.gradle') {
                        details.useVersion gradle.gradleVersion
                    }
                }
            }
        }
    }
}
