# 1. 使用java 11
export PATH=/data/app/jdk11.0.25/bin:$PATH
export JAVA_HOME=/data/app/jdk11.0.25
export PATH=$PATH:$JAVA_HOME/bin
export CLASSPATH=.:$JAVA_HOME/lib:$JAVA_HOME/jre/lib
export PATH=/data/app/jdk11.0.25/bin:$PATH

# 2. 安装基础依赖包
./gradlew --init-script init.gradle dependencies

# 3. 构建项目(跳过测试文件的构建)
./gradlew build -x test

echo "项目构建完成"

# 启动项目
nohup java -jar build/libs/review_pollution_backend-all.jar >> review_pollution_backend.log 2>&1 &

echo "项目启动完成"

