
val kotlin_version: String by project
val logback_version: String by project

allprojects {
    repositories {
        maven {
            url = uri("https://maven.aliyun.com/repository/public/")
        }
        mavenLocal()
        mavenCentral()
    }
}

plugins {
    kotlin("jvm") version "2.1.10"
    id("io.ktor.plugin") version "3.1.3"
    id("org.jetbrains.kotlin.plugin.serialization") version "2.1.10"
}

group = "com.dzhp.permit"
version = "0.0.1"

application {
    mainClass = "io.ktor.server.netty.EngineMain"
}

repositories {
    mavenCentral()
}

dependencies {
    implementation("io.ktor:ktor-server-core")
    implementation("io.ktor:ktor-server-auth")
    implementation("io.ktor:ktor-server-auth-jwt")
    implementation("io.ktor:ktor-server-swagger")
    implementation("io.ktor:ktor-server-sse")
    implementation("io.ktor:ktor-server-content-negotiation")
    implementation("io.ktor:ktor-serialization-kotlinx-json")
    implementation("io.ktor:ktor-serialization-gson")
    implementation("io.ktor:ktor-server-netty")
    implementation("io.ktor:ktor-server-cors") // 添加CORS支持

    // 添加请求日志插件
    implementation("io.ktor:ktor-server-call-logging")
    implementation("ch.qos.logback:logback-classic:$logback_version")
    implementation("org.slf4j:slf4j-api:2.0.9")
    implementation("xerces:xercesImpl:2.12.2")

    implementation("io.ktor:ktor-server-config-yaml")

    // https://mvnrepository.com/artifact/org.jetbrains.exposed/
    implementation("org.jetbrains.exposed:exposed-core:0.60.0")
    implementation("org.jetbrains.exposed:exposed-dao:0.60.0")
    implementation("org.jetbrains.exposed:exposed-jdbc:0.60.0")

    // https://mvnrepository.com/artifact/redis.clients/jedis
    implementation("redis.clients:jedis:6.0.0")

    // https://mvnrepository.com/artifact/com.mysql/mysql-connector-j
    implementation("com.mysql:mysql-connector-j:8.2.0")

    // https://mvnrepository.com/artifact/org.elasticsearch.client/elasticsearch-rest-high-level-client
    implementation("org.elasticsearch.client:elasticsearch-rest-high-level-client:7.10.2")

    // https://mvnrepository.com/artifact/io.ktor/ktor-server-websockets-jvm
    implementation("io.ktor:ktor-server-websockets-jvm:2.3.11")

    // Ktor client for HTTP requests
    implementation("io.ktor:ktor-client-core")
    implementation("io.ktor:ktor-client-cio")
    implementation("io.ktor:ktor-client-content-negotiation")
    implementation("io.ktor:ktor-client-logging")

    // https://mvnrepository.com/artifact/io.ktor/ktor-client-cio-jvm
    implementation("io.ktor:ktor-client-cio-jvm:2.3.12")

    // https://mvnrepository.com/artifact/io.ktor/ktor-client-core-jvm
    implementation("io.ktor:ktor-client-core-jvm:3.0.3")

    // Aliyun SMS SDK
    implementation("com.aliyun:dysmsapi20170525:2.0.24")
    implementation("com.aliyun:tea-openapi:0.2.8")
    implementation("com.aliyun:tea-util:0.2.13")

    // LDAP authentication
    implementation("com.unboundid:unboundid-ldapsdk:6.0.11")

    testImplementation("io.ktor:ktor-server-test-host")
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit:$kotlin_version")
}
