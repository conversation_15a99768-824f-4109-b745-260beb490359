#!/bin/bash

# Script to manually download Gradle distribution from Aliyun mirror
# This can be used if the Gradle wrapper fails to download automatically

GRADLE_VERSION="8.4"
GRADLE_DIST_TYPE="bin"  # can be "bin", "all", or "src"
DOWNLOAD_DIR="$HOME/.gradle/wrapper/dists/gradle-${GRADLE_VERSION}-${GRADLE_DIST_TYPE}"
ALIYUN_URL="https://mirrors.aliyun.com/gradle/distributions/v${GRADLE_VERSION}.0/gradle-${GRADLE_VERSION}-${GRADLE_DIST_TYPE}.zip"

# Create directory structure if it doesn't exist
mkdir -p "$DOWNLOAD_DIR"

# Download the Gradle distribution
echo "Downloading Gradle ${GRADLE_VERSION} from Aliyun mirror..."
curl -L "$ALIYUN_URL" -o "$DOWNLOAD_DIR/gradle-${GRADLE_VERSION}-${GRA<PERSON>LE_DIST_TYPE}.zip"

if [ $? -eq 0 ]; then
    echo "Download successful!"
    echo "Gradle distribution saved to: $DOWNLOAD_DIR/gradle-${GRADLE_VERSION}-${GRADLE_DIST_TYPE}.zip"
    echo "You may need to extract it manually to the correct directory."
else
    echo "Download failed. Please check your internet connection and try again."
fi
