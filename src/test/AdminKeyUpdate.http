### 测试环境变量
@baseUrl = http://localhost:8080
@adminUsername = admin
@adminPassword = admin123

### ---------------------------------------------------------------------------------------------
### 管理员登录
POST {{baseUrl}}/api/admin/login
Content-Type: application/json

{
  "username": "{{adminUsername}}",
  "password": "{{adminPassword}}"
}

> {%
    client.global.set("adminToken", response.body.data.token);
%}

### ---------------------------------------------------------------------------------------------
### 创建新的管理员密钥（一级邀请码）用于测试
POST {{baseUrl}}/api/admin/key/new
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "maxCNYLimit": 10000,
  "status": "ACTIVE",
  "province": "浙江省",
  "city": "杭州市",
  "district": "西湖区",
  "companyName": "测试公司",
  "contractNumber": "HT2023001",
  "contactPerson": "张三",
  "contactPhone": "13800138000",
  "contactEmail": "<EMAIL>",
  "expiresAt": 1777777777777
}

> {%
    client.global.set("adminKeyId", response.body.data.id);
    client.global.set("adminKeyCode", response.body.data.code);
%}

### ---------------------------------------------------------------------------------------------
### 获取管理员密钥详情
GET {{baseUrl}}/api/admin/key/{{adminKeyId}}
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### 更新管理员密钥信息
PUT {{baseUrl}}/api/admin/key/{{adminKeyId}}
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "maxCNYLimit": 20000,
  "status": "ACTIVE",
  "companyName": "更新后的测试公司",
  "contractNumber": "HT2023002-Updated",
  "contactPerson": "李四",
  "contactPhone": "13900139000",
  "contactEmail": "<EMAIL>"
}

### ---------------------------------------------------------------------------------------------
### 再次获取管理员密钥详情，验证更新结果
GET {{baseUrl}}/api/admin/key/{{adminKeyId}}
Authorization: Bearer {{adminToken}}
