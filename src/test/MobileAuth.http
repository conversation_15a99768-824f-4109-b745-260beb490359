### ---------------------------------------------------------------------------------------------
### Environment / variables
@baseUrl = http://localhost:18019
@mobile = 17606593504
@password = Test123456

### ---------------------------------------------------------------------------------------------
### 1. Send verification code
POST {{baseUrl}}/api/user/mobile/send-code
Content-Type: application/json

{
  "mobile": "{{mobile}}"
}

### ---------------------------------------------------------------------------------------------
### 2. Register with mobile and verification code (with password)
# Note: Replace the verification code with the one received from the previous request
# In a real environment, you would receive this via SMS
POST {{baseUrl}}/api/user/mobile/register
Content-Type: application/json

{
  "mobile": "{{mobile}}",
  "verificationCode": "123456",
  "password": "{{password}}"
}

### ---------------------------------------------------------------------------------------------
### 3. Login with mobile and verification code
# Note: Replace the verification code with the one received from a new verification code request
POST {{baseUrl}}/api/user/mobile/login/code
Content-Type: application/json

{
  "mobile": "{{mobile}}",
  "verificationCode": "123456"
}

### ---------------------------------------------------------------------------------------------
### 4. Login with mobile and password
POST {{baseUrl}}/api/user/mobile/login/password
Content-Type: application/json

{
  "mobile": "{{mobile}}",
  "password": "{{password}}"
}

### ---------------------------------------------------------------------------------------------
### 5. Register with mobile and verification code (without password)
# This creates an account that can only be logged in with verification code
# Note: Use a different mobile number to avoid conflicts
POST {{baseUrl}}/api/user/mobile/register
Content-Type: application/json

{
  "mobile": "***********",
  "verificationCode": "123456"
}
