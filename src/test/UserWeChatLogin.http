### ---------------------------------------------------------------------------------------------
### 微信登录测试 - 环境变量
@baseUrl = http://localhost:18019
@validCode = permit_pollution_com
@invalidCode = invalid_code_123
@testState = test_state_parameter

### ---------------------------------------------------------------------------------------------
### 1. 微信登录 - 使用有效授权码
# 预期结果: 成功登录，返回token和用户信息
POST {{baseUrl}}/api/user/wechat/login
Content-Type: application/json

{
  "code": "{{validCode}}",
  "state": "{{testState}}"
}

### ---------------------------------------------------------------------------------------------
### 2. 微信登录 - 使用无效授权码
# 预期结果: 返回错误，无效的授权码
POST {{baseUrl}}/api/user/wechat/login
Content-Type: application/json

{
  "code": "{{invalidCode}}",
  "state": "{{testState}}"
}

### ---------------------------------------------------------------------------------------------
### 3. 微信登录 - 缺少授权码
# 预期结果: 返回错误，请求格式无效
POST {{baseUrl}}/api/user/wechat/login
Content-Type: application/json

{
  "state": "{{testState}}"
}

### ---------------------------------------------------------------------------------------------
### 4. 微信登录 - 使用空授权码
# 预期结果: 返回错误，无效的授权码
POST {{baseUrl}}/api/user/wechat/login
Content-Type: application/json

{
  "code": "",
  "state": "{{testState}}"
}

### ---------------------------------------------------------------------------------------------
### 5. 测试受保护的接口 - 使用微信登录获取的token
# 注意: 需要先运行成功的登录请求，然后从响应中复制token替换下面的YOUR_WECHAT_TOKEN
# 预期结果: 成功访问受保护的接口
GET {{baseUrl}}/api/protected/endpoint
Authorization: Bearer YOUR_WECHAT_TOKEN

### ---------------------------------------------------------------------------------------------
### 6. 微信登录并发测试 - 第一次登录
# 预期结果: 成功登录
POST {{baseUrl}}/api/user/wechat/login
Content-Type: application/json

{
  "code": "{{validCode}}",
  "state": "{{testState}}"
}

### ---------------------------------------------------------------------------------------------
### 7. 微信登录并发测试 - 第二次登录（使用相同的openId）
# 预期结果: 成功登录，但会关闭之前的连接（因为微信登录默认并发数为1）
POST {{baseUrl}}/api/user/wechat/login
Content-Type: application/json

{
  "code": "{{validCode}}",
  "state": "{{testState}}"
}

### ---------------------------------------------------------------------------------------------
### 8. WebSocket连接测试 - 使用微信登录获取的token
# 注意: 这是WebSocket连接，在HTTP Client中无法直接测试
# 可以使用工具如Postman或wscat命令行工具进行实际测试
# 命令示例: wscat -c "ws://localhost:18019/ws/user/connect?token=YOUR_WECHAT_TOKEN"
# 预期结果: 成功建立WebSocket连接
