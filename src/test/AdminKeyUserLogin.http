### ---------------------------------------------------------------------------------------------
### Environment / variables
@baseUrl = http://localhost:18019
@adminKeyCode = ak-704a9dccf32813ec1641b363519003ec
@userToken = 

### ---------------------------------------------------------------------------------------------
### 使用管理员密钥（一级邀请码）登录
POST {{baseUrl}}/api/user/invitation/login
Content-Type: application/json

{
  "invitationCode": "{{adminKeyCode}}"
}

> {%
    client.global.set("userToken", response.body.data.token);
    client.global.set("is_admin", response.body.data.is_admin);
%}

### ---------------------------------------------------------------------------------------------
### 使用管理员密钥获取的token访问用户接口（受user-key-jwt保护）
POST {{baseUrl}}/api/user/logout
Content-Type: application/json
Authorization: Bearer {{userToken}}

{}
