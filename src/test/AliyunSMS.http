### ---------------------------------------------------------------------------------------------
### 阿里云短信测试 - 环境变量
@baseUrl = http://localhost:18019
@mobile = 17606593504
@testPassword = Test123456

### ---------------------------------------------------------------------------------------------
### 1. 发送短信验证码
# 预期结果: 成功发送验证码，返回“验证码已发送”消息
POST {{baseUrl}}/api/user/mobile/send-code
Content-Type: application/json

{
  "mobile": "{{mobile}}"
}

### ---------------------------------------------------------------------------------------------
### 2. 使用手机号和验证码注册（不设置密码）
# 注意: 需要将验证码替换为从上一个请求收到的验证码
# 预期结果: 注册成功，返回token和用户信息
POST {{baseUrl}}/api/user/mobile/register
Content-Type: application/json

{
  "mobile": "{{mobile}}",
  "verificationCode": "123456"
}

### ---------------------------------------------------------------------------------------------
### 3. 使用手机号和验证码注册（设置密码）
# 注意: 需要将验证码替换为从新的验证码请求收到的验证码
# 预期结果: 注册成功，返回token和用户信息
POST {{baseUrl}}/api/user/mobile/register
Content-Type: application/json

{
  "mobile": "{{mobile}}",
  "verificationCode": "123456",
  "password": "{{testPassword}}"
}

### ---------------------------------------------------------------------------------------------
### 4. 使用手机号和验证码登录
# 注意: 需要将验证码替换为从新的验证码请求收到的验证码
# 预期结果: 登录成功，返回token和用户信息
POST {{baseUrl}}/api/user/mobile/login/code
Content-Type: application/json

{
  "mobile": "{{mobile}}",
  "verificationCode": "123456"
}

### ---------------------------------------------------------------------------------------------
### 5. 使用手机号和密码登录
# 预期结果: 登录成功，返回token和用户信息
POST {{baseUrl}}/api/user/mobile/login/password
Content-Type: application/json

{
  "mobile": "{{mobile}}",
  "password": "{{testPassword}}"
}

### ---------------------------------------------------------------------------------------------
### 6. 测试受保护的接口 - 使用手机登录获取的token
# 注意: 需要先运行成功的登录请求，然后从响应中复制token替换下面的YOUR_MOBILE_TOKEN
# 预期结果: 成功访问受保护的接口
GET {{baseUrl}}/api/protected/endpoint
Authorization: Bearer YOUR_MOBILE_TOKEN

### ---------------------------------------------------------------------------------------------
### 7. 发送验证码到无效手机号
# 预期结果: 返回错误，无效的手机号格式
POST {{baseUrl}}/api/user/mobile/send-code
Content-Type: application/json

{
  "mobile": "123"
}

### ---------------------------------------------------------------------------------------------
### 8. 使用错误的验证码登录
# 预期结果: 返回错误，无效的验证码
POST {{baseUrl}}/api/user/mobile/login/code
Content-Type: application/json

{
  "mobile": "{{mobile}}",
  "verificationCode": "000000"
}
