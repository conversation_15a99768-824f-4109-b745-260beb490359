### ---------------------------------------------------------------------------------------------
### 环境变量
@baseUrl = http://localhost:18019
@adminToken =
@adminKeyId =
@adminKeyCode =
@adminKeyToken =eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJwZXJtaXQtYXBpIiwiaXNzIjoiaHR0cHM6Ly9wZXJtaXQuNTFkemhwLmNvbS8iLCJhZG1pbktleUNvZGUiOiIzMzAxMDY3OTMyIiwiYWRtaW5LZXlJZCI6NSwicm9sZSI6ImFkbWluLWtleSIsIm1heENOWUxpbWl0IjoxMDAwMC4wLCJleHAiOjE3NDc5NzkzNjV9.sIqlV3Vehoiltw8V0B92i8hl8BFq_sxFlzatxNC2uBE
@invitationCodeId =

### ---------------------------------------------------------------------------------------------
### 公司内部管理员登录
POST {{baseUrl}}/api/admin/login
Content-Type: application/json

{
  "username": "permitAdmin",
  "password": "1_52Dzhp"
}

> {%
    client.global.set("adminToken", response.body.data.token);
%}

### ---------------------------------------------------------------------------------------------
### 创建新的管理员密钥（一级邀请码）
POST {{baseUrl}}/api/admin/key/new
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "code": "",
  "maxCNYLimit": 10000,
  "status": "ACTIVE",
  "province": "浙江省",
  "city": "杭州市",
  "district": "西湖区",
  "companyName": "测试公司",
  "contractNumber": "HT2023001",
  "contactName": "张三",
  "contactPhone": "13800138000",
  "contactEmail": "<EMAIL>",
  "expiresAt": 1777777777777
}

> {%
    client.global.set("adminKeyId", response.body.data.id);
    client.global.set("adminKeyCode", response.body.data.code);
%}

### ---------------------------------------------------------------------------------------------
### 使用管理员密钥登录
POST {{baseUrl}}/api/admin-key/login
Content-Type: application/json

{
  "adminKeyCode": "3301067932"
}

> {%
    client.global.set("adminKeyToken", response.body.data.token);
%}

### ---------------------------------------------------------------------------------------------
### 测试1: 使用管理员密钥创建新的二级邀请码（省市与管理员密钥一致，区县不同）
### 预期结果: 成功创建，使用管理员密钥的省市，区县使用用户提供的值
POST {{baseUrl}}/api/admin-key/invitation/code/new
Content-Type: application/json
Authorization: Bearer {{adminKeyToken}}

{
  "code": "",
  "userType": "INDIVIDUAL",
  "permissionLevel": "STANDARD",
  "province": "上海市",
  "city": "上海市",
  "district": "黄浦区",
  "status": "ACTIVE",
  "expiresAt": 1654659456789,
  "remainingBalance": 1000,
  "concurrentLimit": 5,
  "companyName": "company123"
}

> {%
    client.global.set("invitationCodeId1", response.body.data.id);
%}

### ---------------------------------------------------------------------------------------------
### 测试2: 使用管理员密钥创建新的二级邀请码（省市与管理员密钥不一致）
### 预期结果: 成功创建，但强制使用管理员密钥的省市，忽略用户提供的省市
POST {{baseUrl}}/api/admin-key/invitation/code/new
Content-Type: application/json
Authorization: Bearer {{adminKeyToken}}

{
  "userType": "INDIVIDUAL",
  "permissionLevel": "STANDARD",
  "province": "上海市",
  "city": "上海市",
  "district": "黄浦区",
  "status": "ACTIVE",
  "expiresAt": {{$timestamp + 30*24*60*60*1000}},
  "remainingBalance": 1000,
  "concurrentLimit": 5,
  "companyName": "测试公司"
}

> {%
    client.global.set("invitationCodeId2", response.body.data.id);
%}

### ---------------------------------------------------------------------------------------------
### 获取当前管理员密钥创建的邀请码列表，验证省市是否被强制设置为管理员密钥的省市
GET {{baseUrl}}/api/admin-key/invitation/code/list
Authorization: Bearer {{adminKeyToken}}

### ---------------------------------------------------------------------------------------------
### 测试3: 修改二级邀请码（尝试修改省市）
### 预期结果: 成功修改，但省市仍然强制使用管理员密钥的值，区县可以修改
PUT {{baseUrl}}/api/admin-key/invitation/code/{{invitationCodeId1}}
Content-Type: application/json
Authorization: Bearer {{adminKeyToken}}

{
  "userType": "COUNTY_ADMINISTRATION",
  "permissionLevel": "ADVANCED",
  "province": "北京市",
  "city": "北京市",
  "district": "朝阳区",
  "status": "ACTIVE",
  "expiresAt": {{$timestamp + 60*24*60*60*1000}},
  "remainingBalance": 2000,
  "concurrentLimit": 10,
  "companyName": "测试公司_更新"
}

### ---------------------------------------------------------------------------------------------
### 再次获取邀请码列表，验证修改后的邀请码省市是否仍然是管理员密钥的省市
GET {{baseUrl}}/api/admin-key/invitation/code/list
Authorization: Bearer {{adminKeyToken}}

### ---------------------------------------------------------------------------------------------
### 删除测试用的二级邀请码
DELETE {{baseUrl}}/api/admin-key/invitation/code/{{invitationCodeId1}}
Authorization: Bearer {{adminKeyToken}}

DELETE {{baseUrl}}/api/admin-key/invitation/code/{{invitationCodeId2}}
Authorization: Bearer {{adminKeyToken}}
