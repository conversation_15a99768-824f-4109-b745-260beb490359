### ---------------------------------------------------------------------------------------------
### 环境变量
@baseUrl = http://localhost:18019
@adminToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJwZXJtaXQtYXBpIiwiaXNzIjoiaHR0cHM6Ly9wZXJtaXQuNTFkemhwLmNvbS8iLCJyb2xlIjoiYWRtaW4iLCJleHAiOjE3NDc4ODEyNTd9.U1VCyLQU5gZlBWifNPuJ_rlYewlyXIzBIDbgtgp4LFI
@adminKeyId = 2
@adminKeyCode = ak-704a9dccf32813ec1641b363519003ec
@invitationCode =

### ---------------------------------------------------------------------------------------------
### 管理员登录
POST {{baseUrl}}/api/admin/login
Content-Type: application/json

{
  "username": "permitAdmin",
  "password": "1_52Dzhp"
}

> {%
    client.global.set("adminToken", response.body.data.token);
%}

### ---------------------------------------------------------------------------------------------
### 创建新的管理员密钥（如果还没有）
POST {{baseUrl}}/api/admin/key/new
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "code": "",
  "maxCNYLimit": 10000,
  "status": "ACTIVE",
  "province": "浙江省",
  "city": "杭州市",
  "district": "西湖区",
  "companyName": "测试公司",
  "contractNumber": "HT2023001",
  "contactPerson": "张三",
  "contactPhone": "13800138000",
  "contactEmail": "<EMAIL>",
  "expiresAt": 1777777777777
}

> {%
    client.global.set("adminKeyId", response.body.data.id);
    client.global.set("adminKeyCode", response.body.data.code);
%}

### ---------------------------------------------------------------------------------------------
### 使用管理员密钥创建新的邀请码
POST {{baseUrl}}/api/admin/new/invitation/code
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "code": "",
  "adminKeyId": {{adminKeyId}},
  "userType": "INDIVIDUAL",
  "permissionLevel": "STANDARD",
  "province": "浙江省",
  "city": "杭州市",
  "district": "西湖区",
  "status": "ACTIVE",
  "expiresAt": {{$timestamp + 30*24*60*60*1000}},
  "remainingBalance": 1000,
  "concurrentLimit": 5,
  "companyName": "测试公司",
  "contactPerson": "张三",
  "contactPhone": "13800138000",
  "contactEmail": "<EMAIL>"
}

> {%
    client.global.set("invitationCode", response.body.data.code);
%}

### ---------------------------------------------------------------------------------------------
### 获取邀请码列表
GET {{baseUrl}}/api/admin/invitation/code/list
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### 获取指定管理员密钥创建的邀请码列表
GET {{baseUrl}}/api/admin/invitation/code/admin-key/{{adminKeyId}}
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### 使用邀请码登录
POST {{baseUrl}}/api/user/invitation/login
Content-Type: application/json

{
  "invitationCode": "{{invitationCode}}"
}

> {%
    client.global.set("userToken", response.body.data.token);
%}

### ---------------------------------------------------------------------------------------------
### 使用WebSocket连接（需要在WebSocket客户端中使用）
# WS {{baseUrl}}/ws/user/connect/{{invitationCode}}?token={{userToken}}
