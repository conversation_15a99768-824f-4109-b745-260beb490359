### ---------------------------------------------------------------------------------------------
### Environment / variables
@baseUrl = http://localhost:18019
@adminKeyToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJwZXJtaXQtYXBpIiwiaXNzIjoiaHR0cHM6Ly9wZXJtaXQuNTFkemhwLmNvbS8iLCJyb2xlIjoiYWRtaW4ta2V5IiwiYWRtaW5LZXlJZCI6MSwiYWRtaW5LZXlDb2RlIjoiYWstMTIzNDU2Nzg5MDEyMzQ1Njc4OTAxMjM0NTY3ODkwMTIiLCJleHAiOjE3NDc3MDkwNjZ9.UeOWRJk4yd80UQ1MQ47sRIfdNZTaXoKUTrTuFqQDUgc

### ---------------------------------------------------------------------------------------------
### 获取当前管理员密钥创建的邀请码的连接情况
GET {{baseUrl}}/api/admin-key/current/linking
Authorization: Bearer {{admin<PERSON><PERSON><PERSON>oken}}

### ---------------------------------------------------------------------------------------------
### 踢出指定邀请码的所有连接
POST {{baseUrl}}/api/admin-key/kick/7AEC1FB2C21C4A70
Authorization: Bearer {{adminKeyToken}}
