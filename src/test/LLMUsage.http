### ---------------------------------------------------------------------------------------------
### Environment / variables
@baseUrl = http://localhost:18019
@ts = {{timestamp}}              # IntelliJ IDEA 内置函数，生成当前毫秒级时间戳

### ---------------------------------------------------------------------------------------------
### Health check: GET /
GET {{baseUrl}}/

### ---------------------------------------------------------------------------------------------
### SSE demo: GET /hello (Server‑Sent Events)
GET {{baseUrl}}/hello
Accept: text/event-stream

### ---------------------------------------------------------------------------------------------
### Single usage record: POST /api/llm-usage
POST {{baseUrl}}/api/llm/usage
Content-Type: application/json

{
  "modelName": "gpt-4o",
  "promptTokens": 120,
  "completionTokens": 250,
  "totalTokens": 370,
  "callBy": "intellij.http.test",
  "userId": "u12345",
  "requestId": "test-request-id-fddskhfnkoiuyew"
}

### ---------------------------------------------------------------------------------------------
### Batch usage records: POST /api/llm-usage/batch
POST {{baseUrl}}/api/llm/usage/batch
Content-Type: application/json

[
  {
    "modelName": "gpt-4o",
    "promptTokens": 100,
    "completionTokens": 200,
    "totalTokens": 300,
    "callBy": "http.batch.1",
    "userId": "user-12345",
    "requestId": "batch-request-id-{{ts}}"
  },
  {
    "modelName": "gemini-pro",
    "promptTokens": 80,
    "completionTokens": 150,
    "totalTokens": 230,
    "callBy": "http.batch.2",
    "userId": "user-12345",
    "requestId": "batch-request-id-{{ts}}"
  }
]
