### ---------------------------------------------------------------------------------------------
### Environment / variables
@baseUrl = ws://localhost:18019
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJwZXJtaXQtYXBpIiwiaXNzIjoiaHR0cHM6Ly9wZXJtaXQuNTFkemhwLmNvbS8iLCJjb2RlIjoiN0FFQzFGQjJDMjFDNEE3MCIsInVzZXJUeXBlIjoiSU5ESVZJRFVBTCIsInBlcm1pc3Npb25MZXZlbCI6IlNUQU5EQVJEIiwiY29tcGFueUlkIjoiempqY2Z6IiwiZXhwIjoxNzQ3NzA5MDY2fQ.UeOWRJk4yd80UQ1MQ47sRIfdNZTaXoKUTrTuFqQDUgc
### ---------------------------------------------------------------------------------------------
### WebSocket连接测试
# 注意: 这是WebSocket连接，在IntelliJ IDEA中需要使用WebSocket客户端功能测试
# 可以使用工具如Postman或wscat命令行工具进行实际测试
# wscat -c "ws://localhost:8080/ws/user/connect/2C048BF6A5E34121?token={{token}}"
WEBSOCKET {{baseUrl}}/ws/user/connect/7AEC1FB2C21C4A70?token={{token}}

###


### ---------------------------------------------------------------------------------------------
### WebSocket连接测试 - 无效令牌
WEBSOCKET {{baseUrl}}/ws/user/connect/7AEC1FB2C21C4A70?token=invalid_token

### ---------------------------------------------------------------------------------------------
### WebSocket连接测试 - 无令牌
WEBSOCKET {{baseUrl}}/ws/user/connect/7AEC1FB2C21C4A70

### ---------------------------------------------------------------------------------------------
### WebSocket连接测试 - 无邀请码
WEBSOCKET {{baseUrl}}/ws/user/connect/?token={{token}}

### ---------------------------------------------------------------------------------------------
### WebSocket管理员监控连接
WEBSOCKET {{baseUrl}}/ws/admin/monitor