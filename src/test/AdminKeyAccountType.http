### ---------------------------------------------------------------------------------------------
### 环境变量
@baseUrl = 127.0.0.1:18019
@adminToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJwZXJtaXQtYXBpIiwiaXNzIjoiaHR0cHM6Ly9wZXJtaXQuNTFkemhwLmNvbS8iLCJyb2xlIjoiYWRtaW4iLCJhZG1pblVzZXJuYW1lIjoic29uZ3h1YW55aSIsImV4cCI6MTc0ODQwMTQ2NH0.0d4Zvf-MYQlDIkciAaH5T4cXAphfdQuEDZfwtdXThmg

### ---------------------------------------------------------------------------------------------

### 管理员登录
POST {{baseUrl}}/api/admin/login
Content-Type: application/json

{
  "username": "songxuanyi",
  "password": "RHq7WBoL"
}

> {%
    client.global.set("adminToken", response.body.data.token);
%}

### ---------------------------------------------------------------------------------------------
### 创建试用账号类型的管理员密钥
POST {{baseUrl}}/api/admin/key/new
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "maxCNYLimit": 5000.0,
  "companyName": "试用公司",
  "accountType": "TRIAL",
  "province": "浙江省",
  "city": "杭州市",
  "district": "西湖区",
  "contractNumber": "TRIAL2024001",
  "contactPerson": "试用联系人",
  "contactPhone": "***********",
  "contactEmail": "<EMAIL>",
  "expiresAt": *************
}

> {%
    client.global.set("trialAdminKeyId", response.body.data.id);
    client.global.set("trialAdminKeyCode", response.body.data.code);
%}

### ---------------------------------------------------------------------------------------------
### 创建正式账号类型的管理员密钥
POST {{baseUrl}}/api/admin/key/new
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "maxCNYLimit": 50000.0,
  "companyName": "正式公司",
  "accountType": "FORMAL",
  "province": "浙江省",
  "city": "杭州市",
  "district": "西湖区",
  "contractNumber": "FORMAL2024001",
  "contactPerson": "正式联系人",
  "contactPhone": "***********",
  "contactEmail": "<EMAIL>",
  "expiresAt": *************
}

> {%
    client.global.set("formalAdminKeyId", response.body.data.id);
    client.global.set("formalAdminKeyCode", response.body.data.code);
%}

### ---------------------------------------------------------------------------------------------
### 获取管理员密钥列表（验证accountType字段）
GET {{baseUrl}}/api/admin/key/list
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### 获取试用账号管理员密钥详情
GET {{baseUrl}}/api/admin/key/{{trialAdminKeyId}}
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### 获取正式账号管理员密钥详情
GET {{baseUrl}}/api/admin/key/{{formalAdminKeyId}}
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### 更新试用账号为正式账号
PUT {{baseUrl}}/api/admin/key/{{trialAdminKeyId}}
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "maxCNYLimit": 10000.0,
  "accountType": "FORMAL",
  "companyName": "升级为正式公司",
  "contractNumber": "UPGRADE2024001",
  "contactPerson": "升级联系人",
  "contactPhone": "***********",
  "contactEmail": "<EMAIL>"
}

### ---------------------------------------------------------------------------------------------
### 使用试用账号管理员密钥登录
POST {{baseUrl}}/api/admin-key/login
Content-Type: application/json

{
  "adminKeyCode": "{{trialAdminKeyCode}}"
}

> {%
    client.global.set("trialAdminKeyToken", response.body.data.token);
%}

### ---------------------------------------------------------------------------------------------
### 使用正式账号管理员密钥登录
POST {{baseUrl}}/api/admin-key/login
Content-Type: application/json

{
  "adminKeyCode": "{{formalAdminKeyCode}}"
}

> {%
    client.global.set("formalAdminKeyToken", response.body.data.token);
%}
