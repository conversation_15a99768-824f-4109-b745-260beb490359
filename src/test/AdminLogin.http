### ---------------------------------------------------------------------------------------------
### Environment / variables
@baseUrl = http://localhost:18019

### ---------------------------------------------------------------------------------------------
### Admin Login with correct credentials
POST {{baseUrl}}/api/admin/login
Content-Type: application/json

{
  "username": "permitAdmin",
  "password": "1_52Dzhp"
}

### ---------------------------------------------------------------------------------------------
### Admin Login with incorrect credentials
POST {{baseUrl}}/api/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrongpassword"
}

### ---------------------------------------------------------------------------------------------
### Test protected endpoint with token (replace with actual token from login response)
GET {{baseUrl}}/api/admin/invitation/code/list
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJwZXJtaXQtYXBpIiwiaXNzIjoiaHR0cHM6Ly9wZXJtaXQuNTFkemhwLmNvbS8iLCJyb2xlIjoiYWRtaW4iLCJleHAiOjE3NDc3MDg4NTd9.lCxQ3rx7Q6w0dBcz7mEzRQ_ZzpXVwZrjBW9BcIEGORk