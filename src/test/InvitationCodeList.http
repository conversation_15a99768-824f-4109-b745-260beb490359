### ---------------------------------------------------------------------------------------------
### Environment / variables
@baseUrl = http://localhost:8080
@adminToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJwZXJtaXQtYXBpIiwiaXNzIjoiaHR0cHM6Ly9wZXJtaXQuNTFkemhwLmNvbS8iLCJyb2xlIjoiYWRtaW4iLCJleHAiOjE3NDcxOTAzNjB9.a5yzh4FOj4BE-RXMPbRyALYESINIY-R2_LaLbHDlNBk

### ---------------------------------------------------------------------------------------------
### Get invitation code list (all)
GET {{baseUrl}}/api/admin/invitation/code/list
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### Get invitation code list (only active)
GET {{baseUrl}}/api/admin/invitation/code/list?onlyActive=true
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### Get invitation code list (including inactive)
GET {{baseUrl}}/api/admin/invitation/code/list?onlyActive=false
Authorization: Bearer {{adminToken}}
