### ---------------------------------------------------------------------------------------------
### 环境变量
@baseUrl = http://localhost:18019
@adminToken =
@adminKeyCode =
@adminKeyToken =
@invitationCodeId =

### ---------------------------------------------------------------------------------------------
### 公司内部管理员登录
POST {{baseUrl}}/api/admin/login
Content-Type: application/json

{
  "username": "permitAdmin",
  "password": "1_52Dzhp"
}

> {%
    client.global.set("adminToken", response.body.data.token);
%}

### ---------------------------------------------------------------------------------------------
### 创建新的管理员密钥（一级邀请码）
POST {{baseUrl}}/api/admin/key/new
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "code": "",
  "maxCNYLimit": 10000,
  "status": "ACTIVE",
  "province": "浙江省",
  "city": "杭州市",
  "district": "西湖区",
  "companyName": "测试公司",
  "contractNumber": "HT2023001",
  "contactPerson": "张三",
  "contactPhone": "13800138000",
  "contactEmail": "<EMAIL>",
  "expiresAt": 1777777777777
}

> {%
    client.global.set("adminKeyId", response.body.data.id);
    client.global.set("adminKeyCode", response.body.data.code);
%}

### ---------------------------------------------------------------------------------------------
### 使用管理员密钥（一级邀请码）登录
POST {{baseUrl}}/api/admin-key/login
Content-Type: application/json

{
  "adminKeyCode": "{{adminKeyCode}}"
}

> {%
    client.global.set("adminKeyToken", response.body.data.token);
%}

### ---------------------------------------------------------------------------------------------
### 使用管理员密钥创建新的二级邀请码
POST {{baseUrl}}/api/admin-key/invitation/code/new
Content-Type: application/json
Authorization: Bearer {{adminKeyToken}}

{
  "code": "",
  "userType": "INDIVIDUAL",
  "permissionLevel": "STANDARD",
  "province": "浙江省",
  "city": "杭州市",
  "district": "西湖区",
  "status": "ACTIVE",
  "expiresAt": {{$timestamp + 30*24*60*60*1000}},
  "remainingBalance": 1000,
  "concurrentLimit": 5,
  "companyName": "测试公司",
  "contactPerson": "张三",
  "contactPhone": "13800138000",
  "contactEmail": "<EMAIL>"
}

> {%
    client.global.set("invitationCode", response.body.data.code);
    client.global.set("invitationCodeId", response.body.data.id);
%}

### ---------------------------------------------------------------------------------------------
### 获取当前管理员密钥创建的邀请码列表
GET {{baseUrl}}/api/admin-key/invitation/code/list
Authorization: Bearer {{adminKeyToken}}

### ---------------------------------------------------------------------------------------------
### 修改二级邀请码
PUT {{baseUrl}}/api/admin-key/invitation/code/{{invitationCodeId}}
Content-Type: application/json
Authorization: Bearer {{adminKeyToken}}

{
  "userType": "COUNTY_ADMINISTRATION",
  "permissionLevel": "ADVANCED",
  "province": "浙江省",
  "city": "宁波市",
  "district": "鄞州区",
  "status": "ACTIVE",
  "expiresAt": {{$timestamp + 60*24*60*60*1000}},
  "remainingBalance": 2000,
  "concurrentLimit": 10,
  "companyName": "测试公司更新",
  "contactName": "李四",
  "contactPhone": "13900139000",
  "contactEmail": "<EMAIL>"
}

### ---------------------------------------------------------------------------------------------
### 删除二级邀请码
DELETE {{baseUrl}}/api/admin-key/invitation/code/{{invitationCodeId}}
Authorization: Bearer {{adminKeyToken}}

### ---------------------------------------------------------------------------------------------
### 使用二级邀请码登录（如果未删除）
POST {{baseUrl}}/api/user/invitation/login
Content-Type: application/json

{
  "invitationCode": "{{invitationCode}}"
}

> {%
    client.global.set("userToken", response.body.data.token);
%}
