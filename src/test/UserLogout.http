### ---------------------------------------------------------------------------------------------
### Environment / variables
@baseUrl = http://localhost:8080
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJwZXJtaXQtYXBpIiwiaXNzIjoiaHR0cHM6Ly9wZXJtaXQuNTFkemhwLmNvbS8iLCJjb2RlIjoiMkMwNDhCRjZBNUUzNDEyMSIsInVzZXJUeXBlIjoiSU5ESVZJRFVBTCIsInBlcm1pc3Npb25MZXZlbCI6IlNUQU5EQVJEIiwiZXhwIjoxNzQ3MTk0MjAwfQ.TOaqAyD1qbuxxs_Kra-cWRWn988HO3kmt5j4yifDa0I
### ---------------------------------------------------------------------------------------------
### User Logout with token in request body
POST {{baseUrl}}/api/user/logout
Content-Type: application/json

{
  "token": "{{token}}"
}

### ---------------------------------------------------------------------------------------------
### User Logout with token in Authorization header
POST {{baseUrl}}/api/user/logout
Content-Type: application/json
Authorization: Bearer {{token}}

### ---------------------------------------------------------------------------------------------
### User Logout with authentication (requires valid token)
POST {{baseUrl}}/api/user/auth/logout
Authorization: Bearer {{token}}

### ---------------------------------------------------------------------------------------------
### Test login after logout (should work if concurrency limit allows)
POST {{baseUrl}}/api/user/login
Content-Type: application/json

{
  "invitationCode": "TEST123456"
}
