### 管理员登录测试 - LDAP认证
POST http://localhost:18019/api/admin/login
Content-Type: application/json

{
  "username": "songxuanyi",
  "password": "RHq7WBoL"
}

### 管理员登录测试 - MySQL认证（用户名）
POST http://localhost:18019/api/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 管理员登录测试 - MySQL认证（手机号）
POST http://localhost:18019/api/admin/login
Content-Type: application/json

{
  "username": "13800138000",
  "password": "admin123"
}

### 管理员登录测试 - 错误密码
POST http://localhost:18019/api/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrongpassword"
}

### 管理员登录测试 - 不存在的用户
POST http://localhost:18019/api/admin/login
Content-Type: application/json

{
  "username": "nonexistentuser",
  "password": "password"
}
