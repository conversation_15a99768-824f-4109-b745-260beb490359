### ---------------------------------------------------------------------------------------------
### Environment / variables
@baseUrl = 127.0.0.1:18019


### ---------------------------------------------------------------------------------------------
### User Login with invitation code
POST {{baseUrl}}/api/user/invitation/login
Content-Type: application/json

{
  "invitationCode": "3301067932"
}
### ---------------------------------------------------------------------------------------------
### Test protected endpoint with token (replace with actual token from login response)
GET {{baseUrl}}/api/protected/endpoint
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJwZXJtaXQtYXBpIiwiaXNzIjoiaHR0cHM6Ly9wZXJtaXQuNTFkemhwLmNvbS8iLCJjb2RlIjoiMkMwNDhCRjZBNUUzNDEyMSIsInVzZXJUeXBlIjoiSU5ESVZJRFVBTCIsInBlcm1pc3Npb25MZXZlbCI6IlNUQU5EQVJEIiwiZXhwIjoxNzQ3MTkyNjU3fQ.NwCM-nyJ0EjYlOGifbTuyEtPHzl-4E0w7yu0k4Iee4E