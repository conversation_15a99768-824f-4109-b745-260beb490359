### ---------------------------------------------------------------------------------------------
### 环境变量
@baseUrl = https://permit.test52dzhp.com/ko/
@adminToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJwZXJtaXQtYXBpIiwiaXNzIjoiaHR0cHM6Ly9wZXJtaXQuNTFkemhwLmNvbS8iLCJyb2xlIjoiYWRtaW4iLCJhZG1pblVzZXJuYW1lIjoiempqY2Z6UGVybWl0QWRtaW4iLCJleHAiOjE3NDc5OTk1NDB9.InUBvTC0jS8_LgjBDUwC2KopD8UVQmByQXoD7GiUtqc
### ---------------------------------------------------------------------------------------------
### 管理员登录
POST {{baseUrl}}/api/admin/login
Content-Type: application/json

{
  "username": "zjjcfzPermitAdmin",
  "password": "1_52Dzhp"
}

> {%
    client.global.set("adminToken", response.body.data.token);
%}

### ---------------------------------------------------------------------------------------------
### 创建新的管理员密钥（自动生成密钥）
POST {{baseUrl}}/api/admin/key/new
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "maxCNYLimit": 100000,
  "status": "ACTIVE",
  "province": "浙江省",
  "city": "杭州市",
  "district": "西湖区",
  "companyName": "浙江聚创方舟",
  "contractNumber": "HT2025001",
  "contactPerson": "李四",
  "contactPhone": "13800138000",
  "contactEmail": "<EMAIL>",
  "expiresAt": 1888887777777
}

> {%
    client.global.set("adminKeyId", response.body.data.id);
    client.global.set("adminKeyCode", response.body.data.code);
%}

### ---------------------------------------------------------------------------------------------
### 获取管理员密钥列表
GET {{baseUrl}}/api/admin/key/list
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### 获取管理员密钥列表（包括停用的）
GET {{baseUrl}}/api/admin/key/list?onlyActive=false
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### 获取指定管理员密钥详情
GET {{baseUrl}}/api/admin/key/{{adminKeyId}}
Authorization: Bearer {{adminToken}}

### ---------------------------------------------------------------------------------------------
### 更新管理员密钥状态
PUT {{baseUrl}}/api/admin/key/{{adminKeyId}}/status
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "status": "DISABLED"
}

### ---------------------------------------------------------------------------------------------
### 删除管理员密钥
DELETE {{baseUrl}}/api/admin/key/{{adminKeyId}}
Authorization: Bearer {{adminToken}}
