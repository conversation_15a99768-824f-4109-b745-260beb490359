### Test Agent Flow Router
POST http://localhost:18019/api/run/flow/permit/mee
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "request_id": "test-request-123",
  "user_id": "test-user-456",
  "agent_list": [
    {
      "agent_id": "test-agent-1",
      "agent_name": "测试代理1",
      "agent_type": "llm"
    }
  ],
  "storage": {
    "username": "test-user",
    "data_id": "test-data-123"
  },
  "permit_mee_data": {
    "test_field": "test_value"
  }
}
