<configuration>
    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- HTTP请求日志专用输出格式 -->
    <appender name="HTTP_STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!-- 简化的格式，只显示消息内容，因为我们已经在LoggingInterceptor中格式化了消息 -->
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>

    <!-- HTTP请求日志记录器 -->
    <logger name="HttpRequest" level="INFO" additivity="false">
        <appender-ref ref="HTTP_STDOUT"/>
    </logger>

    <!-- 根日志记录器 -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>

    <!-- 第三方库日志级别 -->
    <logger name="org.eclipse.jetty" level="INFO"/>
    <logger name="io.netty" level="INFO"/>
    <logger name="io.ktor" level="INFO"/>
    <logger name="Exposed" level="INFO"/>
</configuration>