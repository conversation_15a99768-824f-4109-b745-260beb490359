package com.dzhp.permit

import com.dzhp.permit.models.AdminKeys
import com.dzhp.permit.models.InvitationCodes
import io.ktor.server.application.*
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.SchemaUtils
import com.dzhp.permit.models.LLMUsages
import com.dzhp.permit.models.Transactions
import com.dzhp.permit.models.UserAccounts
import com.dzhp.permit.models.UserIdentities
import com.dzhp.permit.models.UserMfas
import com.dzhp.permit.models.Users
import org.slf4j.LoggerFactory

// 全局MySQL数据库连接
private lateinit var mysqlDatabase: Database

/**
 * 配置MySQL连接
 * 从配置文件读取连接参数并初始化数据库连接
 */
fun Application.configureMySQL() {
    val logger = LoggerFactory.getLogger("MySQL")

    try {
        // 从配置文件获取MySQL连接参数
        val jdbcUrl = environment.config.property("mysql.jdbcUrl").getString()
        val driverClassName = environment.config.property("mysql.driverClassName").getString()
        val username = environment.config.property("mysql.username").getString()
        val password = environment.config.property("mysql.password").getString()

        // 建立数据库连接
        mysqlDatabase = Database.connect(
            url = jdbcUrl,
            driver = driverClassName,
            user = username,
            password = password
        )

        logger.info("Successfully connected to MySQL at $jdbcUrl")

        // 在应用启动时检查并创建表结构
        transaction(mysqlDatabase) {
            SchemaUtils.create(
                LLMUsages,
                AdminKeys,
                InvitationCodes,
                Users,
                UserIdentities,
                UserMfas,
                UserAccounts,
                Transactions
            )
        }
    } catch (e: Exception) {
        logger.error("Error configuring MySQL: ${e.message}", e)
        throw e
    }
}

/**
 * 获取MySQL数据库连接的扩展函数
 * @return MySQL数据库连接实例
 */
fun Application.getMySQLDatabase(): Database = mysqlDatabase

