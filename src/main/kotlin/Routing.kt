package com.dzhp.permit

import com.dzhp.permit.routers.zjjcfzInteral.adminKeyRouter
import com.dzhp.permit.routers.interal.adminLoginRouter
import com.dzhp.permit.routers.admin.adminKeyInvitationCodeRouter
import com.dzhp.permit.routers.admin.adminKeyLoginRouter
import com.dzhp.permit.routers.admin.adminLinkingManageRouter
import com.dzhp.permit.routers.llm.agentFlowRouter
import com.dzhp.permit.routers.llm.llmUsageRouter
import com.dzhp.permit.routers.llm.permitAgentRouter
import com.dzhp.permit.routers.auth.invitation.userLoginRouter
import com.dzhp.permit.routers.auth.userLogoutRouter
import com.dzhp.permit.routers.auth.mobile.userMobileAuthRouter
import com.dzhp.permit.routers.auth.mobile.userMobileOAuthRouter
import com.dzhp.permit.routers.auth.wechat.userWeChatLoginRouter
import com.dzhp.permit.routers.auth.invitation.webSocketRouter
import com.dzhp.permit.service.LLMUsageService
import com.dzhp.permit.services.AdminKeyService
import com.dzhp.permit.services.DistrictService
import com.dzhp.permit.services.InvitationCodeService
import com.dzhp.permit.services.PermitAgentService
import com.dzhp.permit.services.auth.AdminKeyLoginService
import com.dzhp.permit.services.auth.UserInvitationLoginService
import com.dzhp.permit.services.auth.UserLogoutService
import com.dzhp.permit.services.auth.UserMobileLoginService
import com.dzhp.permit.services.auth.UserMobileOAuthService
import com.dzhp.permit.services.auth.UserMobileRegistrationService
import com.dzhp.permit.services.auth.UserMobileVerificationService
import com.dzhp.permit.services.auth.UserWeChatLoginService
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.sse.*
import io.ktor.sse.*

fun Application.configureRouting() {
    /* SSE 插件 */
    install(SSE)

    /* Service 注入 */
    val usageService = LLMUsageService(this)
    val districtService = DistrictService(this)
    val adminKeyService = AdminKeyService(this, districtService)
    val invitationCodeService = InvitationCodeService(this)
    val permitAgentService = PermitAgentService(this)
    val userInvitationLoginService = UserInvitationLoginService(this)
    val userWeChatLoginService = UserWeChatLoginService(this)
    val userLogoutService = UserLogoutService(this)
    val userMobileVerificationService = UserMobileVerificationService(this)
    val userMobileRegistrationService = UserMobileRegistrationService(this)
    val userMobileLoginService = UserMobileLoginService(this)
    val userMobileOAuthService = UserMobileOAuthService(this)
    val adminKeyLoginService = AdminKeyLoginService(this)

    routing {
        get("/") {
            call.respondText("OK")
        }

        sse("/sse") {
            send(ServerSentEvent("Hello"))
            send(ServerSentEvent("SSE"))
        }

        llmUsageRouter(usageService)
        adminKeyRouter(adminKeyService)
        adminLoginRouter()
        adminKeyLoginRouter(adminKeyLoginService)
        adminKeyInvitationCodeRouter(invitationCodeService, adminKeyService)
        userLoginRouter(userInvitationLoginService)
        userWeChatLoginRouter(userWeChatLoginService)
        userLogoutRouter(userLogoutService)
        userMobileAuthRouter(userMobileVerificationService, userMobileRegistrationService, userMobileLoginService)
        userMobileOAuthRouter(userMobileOAuthService)
        permitAgentRouter(permitAgentService)
        webSocketRouter()
        adminLinkingManageRouter(invitationCodeService)
        agentFlowRouter()
    }
}
