package com.dzhp.permit.models

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

/**
 * 标准响应结构
 * 使用泛型参数T来定义data字段的类型
 *
 * @param T data字段的类型，默认为JsonElement
 * @property code HTTP状态码
 * @property message 响应消息
 * @property data 响应数据，可为空
 */
@Serializable
data class StandardResponseStruct<T>(
    val code: Int,
    val message: String,
    val data: T? = null
)

/**
 * 创建标准响应的快捷方法
 *
 * @param code HTTP状态码
 * @param message 响应消息
 * @param data 响应数据，可为空
 * @return 标准响应结构
 */
inline fun <reified T> standardResponse(code: Int, message: String, data: T? = null): StandardResponseStruct<T> {
    return StandardResponseStruct(code, message, data)
}