package com.dzhp.permit.models

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.ReferenceOption

/**
 * 邀请码数据结构
 * 用于访问控制层，通过邀请码机制验证用户身份并分配相应的访问权限和资源限制
 */
@Serializable
data class InvitationCode(
    val id: Long = 0,                       // 主键ID
    val code: String = "",                 // 邀请码唯一标识符
    val adminKeyId: Long? = null,           // 关联的管理员密钥ID
    val userType: UserType,                 // 关联用户类型：县级行政机构/个人用户
    val permissionLevel: PermissionLevel,   // 权限等级：定义访问权限范围

    // 地域限制（省市区三级）
    val province: String? = null,           // 省份
    val city: String? = null,               // 城市
    val district: String? = null,           // 区县

    val regionConstraint: String? = null,   // 地域限制：关联行政区域编码

    val status: InvitationCodeStatus,       // 使用状态：有效/停用/过期
    val expiresAt: Long,                    // 过期时间

    val remainingBalance: Double,           // 邀请码可使用的剩余 TOKEN
    val consumedAmount: Double = 0.0,        // 邀请码已消耗的金额
    val concurrentLimit: Int,               // 允许同时使用该邀请码的最大用户数
    val companyName: String? = null,          // 所属公司名称

    // 联系方式
    val contactPerson: String? = null,        // 联系人姓名
    val contactPhone: String? = null,       // 联系电话
    val contactEmail: String? = null,       // 联系邮箱

    val createBy: String? = null,           // 创建人
    val updateBy: String? = null,           // 更新人
    val deleted: Boolean = false,           // 是否删除 0-未删除 1-已删除

    val createdAt: Long = System.currentTimeMillis(),                    // 创建时间
    val updateAt: Long = System.currentTimeMillis(),                     // 更新时间
)

/**
 * 用户类型枚举
 */
@Serializable
enum class UserType {
    COUNTY_ADMINISTRATION,  // 县级行政机构用户
    INDIVIDUAL              // 个人用户
}

/**
 * 权限等级枚举
 */
@Serializable
enum class PermissionLevel {
    BASIC,      // 基础权限
    STANDARD,   // 标准权限
    ADVANCED,   // 高级权限
    ADMIN       // 管理员权限
}

/**
 * 邀请码状态枚举
 */
@Serializable
enum class InvitationCodeStatus {
    ACTIVE,     // 有效
    DISABLED,   // 停用
    EXPIRED     // 过期
}


/**
 * 邀请码表
 *
 * 存储每个邀请码的权限、余额、并发限制等信息
 */
object InvitationCodes : Table("invitation_code_info") {

    /** 主键 */
    val id = long("id").autoIncrement()

    /** 邀请码字符串，唯一索引 */
    val code = varchar("code", length = 64).uniqueIndex()

    /** 关联的管理员密钥ID */
    val adminKeyId = long("admin_key_id").references(AdminKeys.id, onDelete = ReferenceOption.SET_NULL).nullable()

    /** 用户类型（县级行政机构 / 个人） */
    val userType = enumerationByName("user_type", 32, UserType::class)

    /** 权限等级 */
    val permissionLevel = enumerationByName("permission_level", 16, PermissionLevel::class)

    /** 地域限制（省市区三级） */
    val province = varchar("province", 32).nullable()
    val city = varchar("city", 32).nullable()
    val district = varchar("district", 32).nullable()

    /** 旧版地域限制，可为空（将被废弃） */
    val regionConstraint = varchar("region_constraint", 32).nullable()

    /** 状态：ACTIVE / DISABLED / EXPIRED */
    val status = enumerationByName("status", 16, InvitationCodeStatus::class)

    /** 剩余可用余额（TOKEN） */
    val remainingBalance = double("remaining_balance")

    /** 已消耗金额 */
    val consumedAmount = double("consumed_amount").default(0.0)

    /** 并发使用上限 */
    val concurrentLimit = integer("concurrent_limit")

    /** 所属公司ID */
    val companyName = varchar("company_name", 64).nullable()

    /** 联系方式 */
    val contactPerson = varchar("contact_person", 64).nullable()
    val contactPhone = varchar("contact_phone", 20).nullable()
    val contactEmail = varchar("contact_email", 128).nullable()

    /** 创建 / 更新信息，可为空 */
    val createBy = varchar("create_by", 64).nullable()
    val updateBy = varchar("update_by", 64).nullable()

    /** 软删除标记 */
    val deleted = bool("deleted").default(false)

    /** 时间戳字段（毫秒） */
    val createdAt = long("created_at")
    val expiresAt = long("expires_at")
    val updateAt = long("update_at")

    /** 主键声明 */
    override val primaryKey = PrimaryKey(id, name = "PK_invitation_code_id")

    /** 索引 */
    init {
        index(false, adminKeyId)
    }
}