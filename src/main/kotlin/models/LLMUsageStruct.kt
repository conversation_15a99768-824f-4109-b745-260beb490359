package com.dzhp.permit.models

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.Table


/**
 * LLM使用量数据结构
 * 用于追踪LLM模型使用的token数量和相关统计
 */
@Serializable
data class LLMUsage(
    val id: Long = 0,                     // 唯一 id

    val modelName: String,                // 使用的LLM模型类型
    val promptTokens: Int = 0,            // 提示词token数量
    val completionTokens: Int = 0,        // 补全结果token数量
    val totalTokens: Int = 0,             // 总token数量
    val timestamp: Long = System.currentTimeMillis(), // 使用时间戳（毫秒）

    val callBy: String? = null,           // 从哪个 app 的哪个用途调用, 可为空
    val userId: String? = null,           // 用户ID, 可为空
    val requestId: String? = null,        // 请求ID, 可为空, 用于追踪同一请求的多个LLM调用
) {
    /**
     * 加法运算符重载
     * 用于合并两个相同模型的使用量统计
     *
     * @param other 另一个要合并的LLM使用量对象
     * @return 合并后的新LLM使用量对象
     * @throws IllegalArgumentException 如果两个对象的模型不同
     */
    operator fun plus(other: LLMUsage): LLMUsage {
        if (this.modelName != other.modelName) {
            throw IllegalArgumentException(
                "不能将不同模型的使用量相加: ${this.modelName} 与 ${other.modelName}"
            )
        }

        return LLMUsage(
            modelName = this.modelName,
            promptTokens = this.promptTokens + other.promptTokens,
            completionTokens = this.completionTokens + other.completionTokens,
            totalTokens = this.totalTokens + other.totalTokens,
            userId = this.userId,
            requestId = this.requestId
        )
    }
}


/**
 * LLM使用量日志表
 * 用于记录和追踪各种LLM模型的使用情况和Token消耗
 */
object LLMUsages : Table("llm_usage_log") {
    val id = long("id").autoIncrement()
    val modelName = varchar("model_name", length = 64)
    val promptTokens = integer("prompt_tokens")
    val completionTokens = integer("completion_tokens")
    val totalTokens = integer("total_tokens")
    val timestamp = long("timestamp")
    val callBy = varchar("call_by", length = 255).nullable()
    val userId = varchar("user_id", length = 64).nullable()
    val requestId = varchar("request_id", length = 64).nullable()

    override val primaryKey = PrimaryKey(id, name = "PK_llm_usage_id")
}