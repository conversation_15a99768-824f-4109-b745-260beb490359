package com.dzhp.permit.models

import kotlinx.serialization.Serializable

/**
 * 通用响应数据结构
 * 用于封装常见的API响应数据
 */

/**
 * 登录响应数据
 */
@Serializable
data class LoginResponseData(
    val token: String,
    val expiresIn: Long,
    val userType: String,
    val permissionLevel: String,
    val regionConstraint: String? = null
)

/**
 * 邀请码登录响应数据
 * 包含令牌信息和邀请码的所有字段
 */
@Serializable
data class InvitationLoginResponseData(
    // 令牌信息
    val token: String,
    val expiresIn: Long,
    val is_admin: Boolean = false,          // 是否为管理员（一级邀请码）

    // 邀请码信息
    val id: Long,
    val code: String,
    val adminKeyId: Long? = null,           // 关联的管理员密钥ID
    val userType: String,
    val permissionLevel: String,

    // 地域限制（省市区三级）
    val province: String? = null,           // 省份
    val city: String? = null,               // 城市
    val district: String? = null,           // 区县
    val regionConstraint: String? = null,

    val status: String,
    val remainingBalance: Double,
    val consumedAmount: Double = 0.0,       // 已消耗金额
    val concurrentLimit: Int,
    val companyName: String? = null,

    // 联系方式
    val contactPerson: String? = null,        // 联系人姓名
    val contactPhone: String? = null,       // 联系电话
    val contactEmail: String? = null,       // 联系邮箱

    val createBy: String? = null,
    val updateBy: String? = null,
    val deleted: Boolean,
    val createdAt: Long,
    val expiresAt: Long,
    val updateAt: Long
)

/**
 * 空响应数据
 * 用于不需要返回数据的API响应
 */
@Serializable
object EmptyResponseData
