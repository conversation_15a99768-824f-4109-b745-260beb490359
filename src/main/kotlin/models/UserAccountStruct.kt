package com.dzhp.permit.models

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.Table

/**
 * 用户账户数据结构
 * 存储用户的账户余额和财务信息
 */
@Serializable
data class UserAccount(
    val id: Long = 0,                       // 主键ID
    val userId: Long,                       // 关联用户ID
    val accountType: AccountType,           // 账户类型
    val balance: Double = 0.0,              // 账户余额
    val frozenAmount: Double = 0.0,         // 冻结金额
    val totalRecharge: Double = 0.0,        // 累计充值金额
    val totalConsumption: Double = 0.0,     // 累计消费金额
    val totalGift: Double = 0.0,            // 累计赠送金额
    val currency: String = "CNY",           // 货币类型
    val status: AccountStatus = AccountStatus.NORMAL, // 账户状态
    val lastTransactionAt: Long? = null,    // 最后交易时间

    // 审计字段
    val createBy: String? = null,           // 创建人
    val updateBy: String? = null,           // 更新人
    val deleted: Boolean = false,           // 是否删除
    val createdAt: Long = System.currentTimeMillis(),  // 创建时间
    val updatedAt: Long = System.currentTimeMillis(),  // 更新时间
    val version: Int = 0                    // 乐观锁版本号
)

/**
 * 账户类型枚举
 */
@Serializable
enum class AccountType {
    MAIN,       // 主账户（现金账户）
    GIFT,       // 赠送账户
    CREDIT,     // 信用账户
    POINT,      // 积分账户
    TOKEN       // Token账户（如API调用额度）
}

/**
 * 账户状态枚举
 */
@Serializable
enum class AccountStatus {
    NORMAL,     // 正常
    FROZEN,     // 冻结
    SUSPENDED,  // 暂停
    CLOSED      // 关闭
}

/**
 * 用户账户表
 * 存储用户的账户余额和财务信息
 */
object UserAccounts : Table("user_account") {
    // 基本信息
    val id = long("id").autoIncrement()
    val userId = long("user_id").references(Users.id)
    val accountType = enumerationByName("account_type", 16, AccountType::class)
    val balance = double("balance").default(0.0)
    val frozenAmount = double("frozen_amount").default(0.0)
    val totalRecharge = double("total_recharge").default(0.0)
    val totalConsumption = double("total_consumption").default(0.0)
    val totalGift = double("total_gift").default(0.0)
    val currency = varchar("currency", 16).default("CNY")
    val status = enumerationByName("status", 16, AccountStatus::class).default(AccountStatus.NORMAL)
    val lastTransactionAt = long("last_transaction_at").nullable()

    // 审计字段
    val createBy = varchar("create_by", 64).nullable()
    val updateBy = varchar("update_by", 64).nullable()
    val deleted = bool("deleted").default(false)
    val createdAt = long("created_at")
    val updatedAt = long("updated_at")
    val version = integer("version").default(0)

    // 主键和索引
    override val primaryKey = PrimaryKey(id, name = "PK_user_account_id")

    init {
        uniqueIndex(userId, accountType)
        index(false, userId)
        index(false, status)
    }
}

/**
 * 交易记录数据结构
 * 存储用户账户的交易记录
 */
@Serializable
data class Transaction(
    val id: Long = 0,                       // 主键ID
    val transactionNo: String,              // 交易流水号
    val userId: Long,                       // 关联用户ID
    val accountId: Long,                    // 关联账户ID
    val transactionType: TransactionType,   // 交易类型
    val amount: Double,                     // 交易金额
    val balance: Double,                    // 交易后余额
    val relatedTransactionId: Long? = null, // 关联交易ID
    val status: TransactionStatus = TransactionStatus.SUCCESS, // 交易状态
    val description: String? = null,        // 交易描述
    val metadata: String? = null,           // 元数据（JSON格式）
    val operatorId: String? = null,         // 操作人ID
    val operatorName: String? = null,       // 操作人名称
    val operatorIp: String? = null,         // 操作人IP

    // 审计字段
    val createBy: String? = null,           // 创建人
    val updateBy: String? = null,           // 更新人
    val deleted: Boolean = false,           // 是否删除
    val createdAt: Long = System.currentTimeMillis(),  // 创建时间
    val updatedAt: Long = System.currentTimeMillis()   // 更新时间
)

/**
 * 交易类型枚举
 */
@Serializable
enum class TransactionType {
    RECHARGE,           // 充值
    CONSUMPTION,        // 消费
    REFUND,             // 退款
    GIFT,               // 赠送
    ADJUSTMENT,         // 调整
    TRANSFER_IN,        // 转入
    TRANSFER_OUT,       // 转出
    FREEZE,             // 冻结
    UNFREEZE,           // 解冻
    EXPIRE,             // 过期
    SYSTEM_GRANT        // 系统赠送
}

/**
 * 交易状态枚举
 */
@Serializable
enum class TransactionStatus {
    PENDING,    // 处理中
    SUCCESS,    // 成功
    FAILED,     // 失败
    CANCELLED,  // 已取消
    REFUNDED    // 已退款
}

/**
 * 交易记录表
 * 存储用户账户的交易记录
 */
object Transactions : Table("transaction_record") {
    // 基本信息
    val id = long("id").autoIncrement()
    val transactionNo = varchar("transaction_no", 64).uniqueIndex()
    val userId = long("user_id").references(Users.id)
    val accountId = long("account_id").references(UserAccounts.id)
    val transactionType = enumerationByName("transaction_type", 32, TransactionType::class)
    val amount = double("amount")
    val balance = double("balance")
    val relatedTransactionId = long("related_transaction_id").nullable()
    val status = enumerationByName("status", 16, TransactionStatus::class)
    val description = varchar("description", 255).nullable()
    val metadata = text("metadata").nullable()
    val operatorId = varchar("operator_id", 64).nullable()
    val operatorName = varchar("operator_name", 64).nullable()
    val operatorIp = varchar("operator_ip", 64).nullable()

    // 审计字段
    val createBy = varchar("create_by", 64).nullable()
    val updateBy = varchar("update_by", 64).nullable()
    val deleted = bool("deleted").default(false)
    val createdAt = long("created_at")
    val updatedAt = long("updated_at")

    // 主键和索引
    override val primaryKey = PrimaryKey(id, name = "PK_transaction_id")

    init {
        index(false, userId)
        index(false, accountId)
        index(false, transactionType)
        index(false, status)
        index(false, createdAt)
    }
}

/**
 * 账户余额响应数据
 * 用于API返回用户账户余额信息
 */
@Serializable
data class AccountBalanceResponseData(
    val userId: Long,
    val accountType: String,
    val balance: Double,
    val frozenAmount: Double,
    val availableBalance: Double,
    val currency: String
)

/**
 * 交易记录响应数据
 * 用于API返回交易记录信息
 */
@Serializable
data class TransactionResponseData(
    val id: Long,
    val transactionNo: String,
    val userId: Long,
    val transactionType: String,
    val amount: Double,
    val balance: Double,
    val status: String,
    val description: String?,
    val createdAt: Long
)
