package com.dzhp.permit.models

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonPrimitive

/**
 * 代理系统数据结构
 * 用于存储和管理AI代理的系统提示词和相关配置
 */
@Serializable
data class PermitAgent(
    val roleId: String,                  // 角色ID
    val roleName: String,                // 角色名称
    val roleDescription: String,         // 角色描述
    val roleCreatedAt: Long = System.currentTimeMillis(), // 创建时间
    val roleUpdatedAt: Long = System.currentTimeMillis(), // 更新时间
    val tags: List<String> = emptyList(), // 标签列表
    val isOnline: Int = 1                // 是否上线：1-上线，0-下线
) {
    companion object {
        /**
         * 处理标签字段
         * 将字符串形式的标签转换为列表
         *
         * @param tagsStr 标签字符串
         * @return 标签列表
         */
        fun parseTags(tagsStr: String?): List<String> {
            if (tagsStr.isNullOrEmpty()) {
                return emptyList()
            }
            
            return try {
                val jsonElement = Json.parseToJsonElement(tagsStr)
                if (jsonElement is JsonArray) {
                    jsonElement.jsonArray.mapNotNull { 
                        if (it is JsonPrimitive) it.jsonPrimitive.content else null 
                    }
                } else {
                    emptyList()
                }
            } catch (e: Exception) {
                emptyList()
            }
        }
    }
}

/**
 * 代理系统响应结构
 * 用于API返回代理列表和标签列表
 */
@Serializable
data class PermitAgentListResponse(
    val agents: List<PermitAgent>,
    val total: Int
)

/**
 * 代理系统标签响应结构
 * 用于API返回所有标签列表
 */
@Serializable
data class PermitAgentTagsResponse(
    val tags: List<String>
)
