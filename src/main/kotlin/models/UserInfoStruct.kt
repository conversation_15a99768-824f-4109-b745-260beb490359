package com.dzhp.permit.models

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.Table

/**
 * 用户基本信息数据结构
 * 存储用户的核心基本信息
 */
@Serializable
data class UserInfo(
    val id: Long = 0,                       // 主键ID
    val username: String,                   // 用户名（唯一标识）
    val nickname: String? = null,           // 昵称
    val realName: String? = null,           // 真实姓名
    val avatar: String? = null,             // 头像URL
    val gender: Gender? = null,             // 性别
    val birthdate: String? = null,          // 出生日期 (ISO格式: YYYY-MM-DD)
    val mobile: String? = null,             // 手机号
    val email: String? = null,              // 邮箱
    val address: String? = null,            // 地址
    val idCardNumber: String? = null,       // 身份证号
    val companyName: String? = null,          // 所属公司ID
    val departmentId: String? = null,       // 所属部门ID
    val position: String? = null,           // 职位

    // 用户状态和设置
    val status: UserStatus = UserStatus.ACTIVE,  // 用户状态
    val userType: UserType,                 // 用户类型
    val permissionLevel: PermissionLevel,   // 权限等级
    val regionConstraint: String? = null,   // 地域限制
    val preferredLanguage: String? = null,  // 首选语言
    val timezone: String? = null,           // 时区
    val notificationSettings: String? = null, // 通知设置 (JSON格式)
    val securityLevel: SecurityLevel = SecurityLevel.NORMAL, // 安全等级

    // 账户安全信息
    val passwordUpdatedAt: Long? = null,    // 密码最后更新时间
    val mfaEnabled: Boolean = false,        // 是否启用多因素认证
    val loginFailCount: Int = 0,            // 连续登录失败次数
    val lastLoginIp: String? = null,        // 最后登录IP
    val lastLoginDevice: String? = null,    // 最后登录设备
    val lastLoginAt: Long? = null,          // 最后登录时间
    val registrationIp: String? = null,     // 注册IP

    // 业务相关字段
    val userLevel: Int = 1,                 // 用户等级
    val vipExpireAt: Long? = null,          // VIP过期时间
    val invitationCode: String? = null,     // 注册使用的邀请码
    val invitedBy: Long? = null,            // 邀请人ID
    val tags: String? = null,               // 用户标签 (JSON数组格式)
    val remark: String? = null,             // 备注

    // 审计字段
    val createBy: String? = null,           // 创建人
    val updateBy: String? = null,           // 更新人
    val deleted: Boolean = false,           // 是否删除
    val createdAt: Long = System.currentTimeMillis(),  // 创建时间
    val updatedAt: Long = System.currentTimeMillis(),  // 更新时间
    val version: Int = 0                    // 乐观锁版本号
)

/**
 * 用户状态枚举
 */
@Serializable
enum class UserStatus {
    ACTIVE,      // 活跃
    INACTIVE,    // 未激活
    SUSPENDED,   // 暂停
    LOCKED,      // 锁定
    BANNED,      // 封禁
    DELETED      // 已删除
}

/**
 * 性别枚举
 */
@Serializable
enum class Gender {
    MALE,        // 男
    FEMALE,      // 女
    OTHER,       // 其他
    UNKNOWN      // 未知
}

/**
 * 安全等级枚举
 */
@Serializable
enum class SecurityLevel {
    LOW,         // 低
    NORMAL,      // 中
    HIGH,        // 高
    VERY_HIGH    // 非常高
}

/**
 * 用户表
 * 存储用户基本信息
 */
object Users : Table("user_info") {
    // 基本信息
    val id = long("id").autoIncrement()
    val username = varchar("username", 64).uniqueIndex()
    val nickname = varchar("nickname", 64).nullable()
    val realName = varchar("real_name", 64).nullable()
    val avatar = varchar("avatar", 255).nullable()
    val gender = enumerationByName("gender", 16, Gender::class).nullable()
    val birthdate = varchar("birthdate", 10).nullable() // YYYY-MM-DD格式
    val mobile = varchar("mobile", 20).nullable().uniqueIndex()
    val email = varchar("email", 128).nullable().uniqueIndex()
    val address = varchar("address", 255).nullable()
    val idCardNumber = varchar("id_card_number", 32).nullable()
    val companyName = varchar("company_name", 64).nullable()
    val departmentId = varchar("department_id", 64).nullable()
    val position = varchar("position", 64).nullable()

    // 用户状态和设置
    val status = enumerationByName("status", 16, UserStatus::class).default(UserStatus.ACTIVE)
    val userType = enumerationByName("user_type", 32, UserType::class)
    val permissionLevel = enumerationByName("permission_level", 16, PermissionLevel::class)
    val regionConstraint = varchar("region_constraint", 32).nullable()
    val preferredLanguage = varchar("preferred_language", 16).nullable()
    val timezone = varchar("timezone", 32).nullable()
    val notificationSettings = text("notification_settings").nullable()
    val securityLevel = enumerationByName("security_level", 16, SecurityLevel::class).default(SecurityLevel.NORMAL)

    // 账户安全信息
    val passwordUpdatedAt = long("password_updated_at").nullable()
    val mfaEnabled = bool("mfa_enabled").default(false)
    val loginFailCount = integer("login_fail_count").default(0)
    val lastLoginIp = varchar("last_login_ip", 64).nullable()
    val lastLoginDevice = varchar("last_login_device", 255).nullable()
    val lastLoginAt = long("last_login_at").nullable()
    val registrationIp = varchar("registration_ip", 64).nullable()

    // 业务相关字段
    val userLevel = integer("user_level").default(1)
    val vipExpireAt = long("vip_expire_at").nullable()
    val invitationCode = varchar("invitation_code", 64).nullable()
    val invitedBy = long("invited_by").nullable()
    val tags = text("tags").nullable()
    val remark = text("remark").nullable()

    // 审计字段
    val createBy = varchar("create_by", 64).nullable()
    val updateBy = varchar("update_by", 64).nullable()
    val deleted = bool("deleted").default(false)
    val createdAt = long("created_at")
    val updatedAt = long("updated_at")
    val version = integer("version").default(0)

    // 主键
    override val primaryKey = PrimaryKey(id, name = "PK_user_id")

    // 索引
    init {
        index(false, companyName)
        index(false, departmentId)
        index(false, userType)
        index(false, status)
        index(false, userLevel)
    }
}
