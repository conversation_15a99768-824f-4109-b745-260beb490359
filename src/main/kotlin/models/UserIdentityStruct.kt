package com.dzhp.permit.models

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.Table

/**
 * 用户身份认证数据结构
 * 存储用户的身份认证信息，支持多种登录方式
 */
@Serializable
data class UserIdentity(
    val id: Long = 0,                       // 主键ID
    val userId: Long,                       // 关联用户ID
    val identityType: IdentityType,         // 身份类型：微信/手机号/邮箱等
    val identifier: String,                 // 身份标识：微信openid/手机号/邮箱等
    val credential: String? = null,         // 凭证：密码哈希/token等，微信登录可为空
    val isVerified: Boolean = false,        // 是否已验证
    val verifiedAt: Long? = null,           // 验证时间
    val verificationCode: String? = null,   // 验证码（仅在验证过程中使用）
    val verificationCodeExpireAt: Long? = null, // 验证码过期时间
    val lastUsedAt: Long? = null,           // 最后使用时间
    val failedAttempts: Int = 0,            // 失败尝试次数
    val lockedUntil: Long? = null,          // 锁定至什么时间
    val priority: Int = 0,                  // 优先级（用于多种登录方式的排序）
    val isPrimary: Boolean = false,         // 是否为主要身份
    val extraData: String? = null,          // 额外数据：如微信unionId等，JSON格式存储

    // 审计字段
    val createBy: String? = null,           // 创建人
    val updateBy: String? = null,           // 更新人
    val deleted: Boolean = false,           // 是否删除
    val createdAt: Long = System.currentTimeMillis(),  // 创建时间
    val updatedAt: Long = System.currentTimeMillis(),  // 更新时间
    val version: Int = 0                    // 乐观锁版本号
)

/**
 * 身份类型枚举
 */
@Serializable
enum class IdentityType {
    WECHAT,     // 微信登录
    MOBILE,     // 手机号登录
    EMAIL,      // 邮箱登录
    USERNAME,   // 用户名密码登录
    INVITATION, // 邀请码登录
    QQ,         // QQ登录
    WEIBO,      // 微博登录
    GITHUB,     // GitHub登录
    APPLE,      // Apple登录
    GOOGLE,     // Google登录
    FACEBOOK,   // Facebook登录
    LDAP,       // LDAP登录
    SAML,       // SAML登录
    OAUTH2,     // OAuth2通用登录
    API_KEY     // API密钥认证
}

/**
 * 用户身份认证表
 * 存储用户的多种登录身份信息
 */
object UserIdentities : Table("user_identity") {
    // 基本信息
    val id = long("id").autoIncrement()
    val userId = long("user_id").references(Users.id)
    val identityType = enumerationByName("identity_type", 16, IdentityType::class)
    val identifier = varchar("identifier", 128)
    val credential = varchar("credential", 255).nullable()
    val isVerified = bool("is_verified").default(false)
    val verifiedAt = long("verified_at").nullable()
    val verificationCode = varchar("verification_code", 64).nullable()
    val verificationCodeExpireAt = long("verification_code_expire_at").nullable()
    val lastUsedAt = long("last_used_at").nullable()
    val failedAttempts = integer("failed_attempts").default(0)
    val lockedUntil = long("locked_until").nullable()
    val priority = integer("priority").default(0)
    val isPrimary = bool("is_primary").default(false)
    val extraData = text("extra_data").nullable()

    // 审计字段
    val createBy = varchar("create_by", 64).nullable()
    val updateBy = varchar("update_by", 64).nullable()
    val deleted = bool("deleted").default(false)
    val createdAt = long("created_at")
    val updatedAt = long("updated_at")
    val version = integer("version").default(0)

    // 主键和索引
    override val primaryKey = PrimaryKey(id, name = "PK_user_identity_id")

    // 复合唯一索引：确保每种身份类型下的标识符唯一
    init {
        uniqueIndex(identityType, identifier)
        index(false, userId)
        index(false, isPrimary)
        index(false, lastUsedAt)
    }
}

/**
 * 用户多因素认证数据结构
 * 存储用户的多因素认证信息
 */
@Serializable
data class UserMfa(
    val id: Long = 0,                       // 主键ID
    val userId: Long,                       // 关联用户ID
    val mfaType: MfaType,                  // MFA类型
    val secret: String,                     // 密钥
    val backupCodes: String? = null,        // 备用码（JSON数组格式）
    val isEnabled: Boolean = true,          // 是否启用
    val lastUsedAt: Long? = null,           // 最后使用时间

    // 审计字段
    val createBy: String? = null,           // 创建人
    val updateBy: String? = null,           // 更新人
    val deleted: Boolean = false,           // 是否删除
    val createdAt: Long = System.currentTimeMillis(),  // 创建时间
    val updatedAt: Long = System.currentTimeMillis()   // 更新时间
)

/**
 * MFA类型枚举
 */
@Serializable
enum class MfaType {
    TOTP,       // 基于时间的一次性密码（如Google Authenticator）
    SMS,        // 短信验证码
    EMAIL,      // 邮件验证码
    BACKUP_CODE // 备用码
}

/**
 * 用户多因素认证表
 * 存储用户的多因素认证信息
 */
object UserMfas : Table("user_mfa") {
    // 基本信息
    val id = long("id").autoIncrement()
    val userId = long("user_id").references(Users.id)
    val mfaType = enumerationByName("mfa_type", 16, MfaType::class)
    val secret = varchar("secret", 255)
    val backupCodes = text("backup_codes").nullable()
    val isEnabled = bool("is_enabled").default(true)
    val lastUsedAt = long("last_used_at").nullable()

    // 审计字段
    val createBy = varchar("create_by", 64).nullable()
    val updateBy = varchar("update_by", 64).nullable()
    val deleted = bool("deleted").default(false)
    val createdAt = long("created_at")
    val updatedAt = long("updated_at")

    // 主键和索引
    override val primaryKey = PrimaryKey(id, name = "PK_user_mfa_id")

    init {
        uniqueIndex(userId, mfaType)
        index(false, userId)
    }
}

/**
 * 用户身份认证响应数据
 * 用于API返回用户身份信息
 */
@Serializable
data class UserIdentityResponseData(
    val userId: Long,
    val identityType: String,
    val identifier: String,
    val isVerified: Boolean,
    val isPrimary: Boolean = false
)
