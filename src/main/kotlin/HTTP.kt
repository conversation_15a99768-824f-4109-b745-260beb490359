package com.dzhp.permit

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.plugins.cors.routing.*
import io.ktor.server.plugins.swagger.*
import io.ktor.server.routing.*

/**
 * 检测当前运行环境
 * @return true 如果是测试环境，false 如果是生产环境
 */
fun Application.isTestEnvironment(): Boolean {
    val env = environment.config.property("ktor.environment").getString()
    return env == "test"
}

fun Application.configureHTTP() {
    // 配置请求日志
    configureRequestLogging()

    // 配置CORS
    install(CORS) {
        // 允许所有主机的请求
        anyHost()

        // 允许的HTTP方法
        allowMethod(HttpMethod.Options)
        allowMethod(HttpMethod.Get)
        allowMethod(HttpMethod.Post)
        allowMethod(HttpMethod.Put)
        allowMethod(HttpMethod.Delete)
        allowMethod(HttpMethod.Patch)

        // 允许的HTTP头
        allowHeader(HttpHeaders.Authorization)
        allowHeader(HttpHeaders.ContentType)
        allowHeader(HttpHeaders.Accept)

        // 允许发送凭证（如cookies）
        allowCredentials = true

        // 允许的最大缓存时间（秒）
        maxAgeInSeconds = 3600
    }

    // 只在测试环境中启用Swagger UI
    if (isTestEnvironment()) {
        routing {
            swaggerUI(path = "openapi", swaggerFile = "openapi/documentation.yaml")
        }
    }
}
