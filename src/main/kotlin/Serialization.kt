package com.dzhp.permit

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import io.ktor.serialization.gson.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.plugins.swagger.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.sse.*
import io.ktor.sse.*
import kotlinx.serialization.json.Json

/**
 * 配置序列化
 * 设置内容协商和序列化选项
 */
fun Application.configureSerialization() {
    install(ContentNegotiation) {
        // 使用kotlinx.serialization进行JSON序列化
        json(Json {
            // 允许序列化未知的键（对于泛型很重要）
            ignoreUnknownKeys = true
            // 允许序列化多态对象
            isLenient = true
            // 允许序列化空对象
            encodeDefaults = true
            // 允许序列化特殊浮点值（如NaN、Infinity）
            allowSpecialFloatingPointValues = true
            // 使用松散的类型检查
            coerceInputValues = true
            // 使用美观的输出格式
            prettyPrint = false
        })

        // 使用Gson进行JSON序列化（可选）
        gson {
            // 这里可以添加Gson的配置
            setPrettyPrinting()
            serializeNulls()
        }
    }

    // 测试路由
    routing {
        get("/json/kotlinx-serialization") {
            call.respond(mapOf("hello" to "world"))
        }
        get("/json/gson") {
            call.respond(mapOf("hello" to "world"))
        }
    }
}
