package com.dzhp.permit

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.plugins.calllogging.*
import io.ktor.server.plugins.origin
import io.ktor.util.*
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.slf4j.event.Level

/**
 * 请求时间拦截器
 * 用于测量请求处理时间并将其存储在MDC中
 */
val RequestTimingInterceptor = createApplicationPlugin(name = "RequestTimingInterceptor") {
    val requestStartTimeKey = AttributeKey<Long>("request_start_time")

    onCall { call ->
        // 记录请求开始时间
        val startTime = System.currentTimeMillis()
        call.attributes.put(requestStartTimeKey, startTime)
    }

    onCallRespond { call ->
        // 在响应发送前计算并记录请求时间
        val startTime = call.attributes.getOrNull(requestStartTimeKey) ?: return@onCallRespond
        val duration = System.currentTimeMillis() - startTime
        MDC.put("request_duration", duration.toString())
    }
}

/**
 * 配置请求日志
 * 设置HTTP请求日志格式，类似Rust Rocket框架的日志输出
 */
fun Application.configureRequestLogging() {

    // 安装请求时间拦截器
    install(RequestTimingInterceptor)

    install(CallLogging) {
        level = Level.INFO

        // 创建一个专门的Logger用于HTTP请求日志
        val httpLogger = LoggerFactory.getLogger("HttpRequest")
        logger = httpLogger

        // 自定义日志格式
        format { call ->
            val status = call.response.status() ?: HttpStatusCode.InternalServerError
            val method = call.request.httpMethod.value
            val path = call.request.path()
            val contentType = try {
                call.request.contentType().toString()
            } catch (_: Exception) {
                "unknown/unknown"
            }
            val remoteHost = call.request.origin.remoteHost

            // 从MDC获取请求处理时间
            val duration = MDC.get("request_duration")?.toLongOrNull() ?: 0L

            // 构建类似Rocket的日志格式
            // 格式: [状态码 状态文本] [耗时ms] 请求方法 路径 (来源IP) [内容类型]
            buildString {
                // 状态码部分，使用颜色标记不同状态
                append("[")
                when {
                    status.value < 300 -> append("\u001B[32m") // 绿色表示成功
                    status.value < 400 -> append("\u001B[33m") // 黄色表示重定向
                    else -> append("\u001B[31m") // 红色表示错误
                }
                append("${status.value} ${status.description}")
                append("\u001B[0m") // 重置颜色
                append("] ")

                // 请求耗时
                append("[")
                if (duration > 1000) {
                    append("\u001B[31m") // 红色表示慢请求
                } else if (duration > 500) {
                    append("\u001B[33m") // 黄色表示中等速度
                } else {
                    append("\u001B[32m") // 绿色表示快速请求
                }
                append("${duration}ms")
                append("\u001B[0m") // 重置颜色
                append("] ")

                // 请求方法和路径
                append("\u001B[1m") // 粗体
                append("$method $path")
                append("\u001B[0m") // 重置样式

                // 来源IP
                append(" (")
                append(remoteHost)
                append(") ")

                // 内容类型
                append("[")
                append(contentType)
                append("]")
            }
        }

        // 过滤掉健康检查和静态资源请求
        filter { call ->
            !call.request.path().startsWith("/health") &&
            !call.request.path().startsWith("/static") &&
            !call.request.path().startsWith("/favicon.ico")
        }
    }
}
