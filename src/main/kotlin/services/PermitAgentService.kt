package com.dzhp.permit.services

import com.dzhp.permit.models.PermitAgent
import com.google.gson.Gson
import io.ktor.server.application.*
import org.elasticsearch.action.search.SearchRequest
import org.elasticsearch.client.RequestOptions
import org.elasticsearch.client.RestHighLevelClient
import org.elasticsearch.index.query.QueryBuilders
import org.elasticsearch.search.builder.SearchSourceBuilder
import org.slf4j.LoggerFactory
import com.dzhp.permit.getElasticSearchClient
import kotlinx.serialization.json.Json
import org.elasticsearch.search.SearchHit
import java.util.*

/**
 * 代理系统服务
 * 提供与代理系统相关的功能，包括获取代理列表和标签列表
 */
class PermitAgentService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val gson = Gson()
    private val client: RestHighLevelClient
        get() = application.getElasticSearchClient()
    
    companion object {
        private const val AGENT_INDEX = "agent_system_prompt_center"
    }

    /**
     * 获取代理列表
     * 从Elasticsearch中获取所有在线的代理
     *
     * @return 代理列表
     */
    fun searchAgentList(): List<PermitAgent> {
        try {
            // 构建查询
            val searchSourceBuilder = SearchSourceBuilder()
                .query(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("is_online", 1))
                )
                .size(10000)
            
            // 设置返回字段
            searchSourceBuilder.fetchSource(
                arrayOf("role_id", "role_name", "role_description", "role_created_at", "role_updated_at", "tags"),
                null
            )
            
            // 创建搜索请求
            val searchRequest = SearchRequest(AGENT_INDEX)
                .source(searchSourceBuilder)
            
            // 执行搜索
            val response = client.search(searchRequest, RequestOptions.DEFAULT)
            
            // 处理结果
            if ((response.hits.totalHits?.value ?: 0) > 0) {
                return response.hits.hits.map { hit ->
                    val source = hit.sourceAsMap
                    
                    // 处理tags字段
                    val tags = source["tags"]
                    val tagsList = when (tags) {
                        is String -> PermitAgent.parseTags(tags)
                        is List<*> -> tags.filterIsInstance<String>()
                        else -> emptyList()
                    }
                    
                    PermitAgent(
                        roleId = source["role_id"]?.toString() ?: "",
                        roleName = source["role_name"]?.toString() ?: "",
                        roleDescription = source["role_description"]?.toString() ?: "",
                        roleCreatedAt = source["role_created_at"]?.toString()?.toLongOrNull() ?: 0L,
                        roleUpdatedAt = source["role_updated_at"]?.toString()?.toLongOrNull() ?: 0L,
                        tags = tagsList,
                        isOnline = 1
                    )
                }
            }
        } catch (e: Exception) {
            logger.error("Error searching agent list: ${e.message}", e)
        }
        
        return emptyList()
    }

    /**
     * 获取所有代理标签
     * 从Elasticsearch中获取所有在线代理的标签，并去重
     *
     * @return 标签列表
     */
    fun searchAgentTagList(): List<String> {
        val result = mutableSetOf<String>()
        
        try {
            // 构建查询
            val searchSourceBuilder = SearchSourceBuilder()
                .query(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("is_online", 1))
                )
                .size(10000)
            
            // 只获取tags字段
            searchSourceBuilder.fetchSource(arrayOf("tags"), null)
            
            // 创建搜索请求
            val searchRequest = SearchRequest(AGENT_INDEX)
                .source(searchSourceBuilder)
            
            // 执行搜索
            val response = client.search(searchRequest, RequestOptions.DEFAULT)
            
            // 处理结果
            if ((response.hits.totalHits?.value ?: 0) > 0) {
                response.hits.hits.forEach { hit ->
                    val source = hit.sourceAsMap
                    val tags = source["tags"]
                    
                    when (tags) {
                        is String -> {
                            val parsedTags = PermitAgent.parseTags(tags)
                            result.addAll(parsedTags)
                        }
                        is List<*> -> {
                            tags.filterIsInstance<String>().forEach { tag ->
                                result.add(tag)
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("Error searching agent tag list: ${e.message}", e)
        }
        
        return result.toList()
    }
}
