package com.dzhp.permit.service

import com.dzhp.permit.getMySQLDatabase
import com.dzhp.permit.models.LLMUsage
import com.dzhp.permit.models.LLMUsages
import io.ktor.server.application.*
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.LoggerFactory

/**
 * LLM使用量服务
 * 负责记录和管理LLM模型的使用量统计
 */
class LLMUsageService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val db: Database
        get() = application.getMySQLDatabase()

    /**
     * 写入单条使用记录
     *
     * @param usage  待写入的使用记录
     * @return       数据库生成的主键 ID
     */
    fun recordUsage(usage: LLMUsage): Long =
        transaction(db) {
            LLMUsages.insert {
                it[modelName] = usage.modelName
                it[promptTokens] = usage.promptTokens
                it[completionTokens] = usage.completionTokens
                it[totalTokens] = usage.totalTokens
                it[timestamp] = usage.timestamp
                it[callBy] = usage.callBy
                it[userId] = usage.userId
                it[requestId] = usage.requestId
            } get LLMUsages.id
        }

    /**
     * 批量写入（可选）
     *
     * @param usages  若干条使用记录
     * @return        成功写入的行数
     */
    fun recordBatch(usages: Iterable<LLMUsage>): Int =
        transaction(db) {
            usages.sumOf { usage ->
                LLMUsages.insert {
                    it[modelName] = usage.modelName
                    it[promptTokens] = usage.promptTokens
                    it[completionTokens] = usage.completionTokens
                    it[totalTokens] = usage.totalTokens
                    it[timestamp] = usage.timestamp
                    it[callBy] = usage.callBy
                    it[userId] = usage.userId
                    it[requestId] = usage.requestId
                }.insertedCount
            }
        }
}