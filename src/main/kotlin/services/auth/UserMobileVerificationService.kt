package com.dzhp.permit.services.auth

import io.ktor.server.application.*
import com.dzhp.permit.getRedisConnection
import com.dzhp.permit.services.AliyunSMSService
import com.dzhp.permit.services.SMSSendResult
import org.slf4j.LoggerFactory
import kotlin.random.Random

/**
 * 手机验证码服务
 * 负责生成、存储和验证手机验证码
 */
class UserMobileVerificationService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val smsService = AliyunSMSService(application)

    companion object {
        // Redis键前缀，用于区分不同类型的数据
        private const val MOBILE_VERIFICATION_CODE_PREFIX = "mobile:verification:"

        // 验证码的默认过期时间（5分钟，单位：秒）
        private const val DEFAULT_CODE_EXPIRY = 5 * 60

        // 验证码长度
        private const val VERIFICATION_CODE_LENGTH = 6
    }

    /**
     * 生成并存储手机验证码
     *
     * @param mobile 手机号
     * @return 生成的验证码
     */
    fun generateAndStoreVerificationCode(mobile: String): String {
        // 生成随机验证码
        val code = generateRandomCode()

        // 存储到Redis
        application.getRedisConnection().use { jedis ->
            val key = getVerificationCodeKey(mobile)
            jedis.setex(key, DEFAULT_CODE_EXPIRY.toLong(), code)
        }

        // 调用阿里云短信服务发送验证码
        val sendResult = smsService.sendVerificationCode(mobile, code)

        when (sendResult) {
            is SMSSendResult.Success -> {
                logger.info("向手机号 $mobile 发送验证码成功，消息ID: ${sendResult.messageId}")
            }
            is SMSSendResult.ApiError -> {
                logger.warn("向手机号 $mobile 发送验证码失败: ${sendResult.code} - ${sendResult.message}")
            }
            is SMSSendResult.Error -> {
                logger.error("向手机号 $mobile 发送验证码错误: ${sendResult.message}")
            }
        }

        return code
    }

    /**
     * 验证手机验证码
     *
     * @param mobile 手机号
     * @param code 用户提供的验证码
     * @return 验证是否成功
     */
    fun verifyCode(mobile: String, code: String): Boolean {
        application.getRedisConnection().use { jedis ->
            val key = getVerificationCodeKey(mobile)
            val storedCode = jedis.get(key)

            // 验证成功后删除验证码，防止重复使用
            if (storedCode == code) {
                jedis.del(key)
                return true
            }

            return false
        }
    }

    /**
     * 获取验证码的Redis键
     *
     * @param mobile 手机号
     * @return Redis键
     */
    private fun getVerificationCodeKey(mobile: String): String {
        return "$MOBILE_VERIFICATION_CODE_PREFIX$mobile"
    }

    /**
     * 生成随机验证码
     *
     * @return 生成的随机验证码
     */
    private fun generateRandomCode(): String {
        return (0 until VERIFICATION_CODE_LENGTH)
            .map { Random.nextInt(0, 10) }
            .joinToString("")
    }
}
