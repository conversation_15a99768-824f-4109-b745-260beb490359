package com.dzhp.permit.services.auth

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.exceptions.JWTVerificationException
import com.dzhp.permit.getWebSocketConnectionManager
import io.ktor.server.application.*
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory

/**
 * 用户登出服务
 * 负责用户登出和清理WebSocket连接
 */
class UserLogoutService(
    private val application: Application
) {
    private val webSocketConnectionManager = application.getWebSocketConnectionManager()
    private val logger = LoggerFactory.getLogger(javaClass)
    private val jwtSecret: String
        get() = application.environment.config.property("jwt.secret").getString()
    private val jwtIssuer: String
        get() = application.environment.config.property("jwt.domain").getString()
    private val jwtAudience: String
        get() = application.environment.config.property("jwt.audience").getString()
    /**
     * 用户登出结果
     */
    sealed class LogoutResult {
        /** 登出成功 */
        object Success : LogoutResult()

        /** 无效的令牌 */
        object InvalidToken : LogoutResult()

        /** 令牌已过期 */
        object TokenExpired : LogoutResult()

        /** 会话不存在 */
        object SessionNotFound : LogoutResult()
    }

    /**
     * 用户登出
     *
     * @param token JWT令牌
     * @return 登出结果
     */
    fun logout(token: String): LogoutResult {
        try {
            // 验证并解析JWT令牌
            val verifier = JWT.require(Algorithm.HMAC256(jwtSecret))
                .withAudience(jwtAudience)
                .withIssuer(jwtIssuer)
                .build()

            val jwt = verifier.verify(token)

            // 从JWT中获取邀请码
            val invitationCode = jwt.getClaim("code").asString()
            if (invitationCode.isNullOrEmpty()) {
                return LogoutResult.InvalidToken
            }

            // 使用WebSocket连接管理器移除连接
            val removed = runBlocking {
                webSocketConnectionManager.removeConnectionByToken(token)
            }

            return if (removed) {
                LogoutResult.Success
            } else {
                // 如果没有找到连接，可能是用户没有建立WebSocket连接
                // 我们仍然返回成功，因为用户可能只是登出而没有建立连接
                LogoutResult.Success
            }
        } catch (e: JWTVerificationException) {
            // JWT验证失败
            logger.warn("JWT verification failed during logout: ${e.message}")
            return LogoutResult.InvalidToken
        } catch (e: Exception) {
            // 其他异常
            logger.error("Error during logout: ${e.message}", e)
            return LogoutResult.InvalidToken
        }
    }
}
