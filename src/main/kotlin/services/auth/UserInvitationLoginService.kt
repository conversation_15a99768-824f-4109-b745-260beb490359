package com.dzhp.permit.services.auth

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.dzhp.permit.getWebSocketConnectionManager
import com.dzhp.permit.models.AdminKey
import com.dzhp.permit.models.AdminKeyStatus
import com.dzhp.permit.models.InvitationCode
import com.dzhp.permit.models.UserType
import com.dzhp.permit.services.AdminKeyService
import com.dzhp.permit.services.AdminKeyValidationResult
import com.dzhp.permit.services.DistrictService
import com.dzhp.permit.services.InvitationCodeService
import com.dzhp.permit.services.InvitationCodeValidationResult
import io.ktor.server.application.*
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 用户登录服务
 * 负责用户登录验证和JWT令牌生成
 */
class UserInvitationLoginService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val invitationCodeService = InvitationCodeService(application)
    private val districtService = DistrictService(application)
    private val adminKeyService = AdminKeyService(application, districtService)
    private val webSocketConnectionManager = application.getWebSocketConnectionManager()
    private val jwtSecret: String
        get() = application.environment.config.property("jwt.secret").getString()
    private val jwtIssuer: String
        get() = application.environment.config.property("jwt.domain").getString()
    private val jwtAudience: String
        get() = application.environment.config.property("jwt.audience").getString()

    /**
     * 用户登录结果
     */
    sealed class LoginResult {
        /** 登录成功 - 使用二级邀请码 */
        data class Success(
            val token: String,
            val expiresIn: Long,
            val invitationCode: InvitationCode,
            val isAdmin: Boolean = false
        ) : LoginResult()

        /** 登录成功 - 使用管理员密钥（一级邀请码） */
        data class AdminKeySuccess(
            val token: String,
            val expiresIn: Long,
            val adminKey: AdminKey
        ) : LoginResult()

        /** 邀请码不存在 */
        object CodeNotFound : LoginResult()

        /** 邀请码已停用 */
        object CodeInactive : LoginResult()

        /** 邀请码已过期 */
        object CodeExpired : LoginResult()

        /** 邀请码余额不足 */
        object InsufficientBalance : LoginResult()

        /** 并发限制超过 */
        object ConcurrencyLimitExceeded : LoginResult()
    }

    /**
     * 使用邀请码登录
     * 支持二级邀请码和管理员密钥（一级邀请码）
     *
     * @param code 邀请码或管理员密钥
     * @return 登录结果
     */
    fun loginWithInvitationCode(code: String): LoginResult {
        // 先尝试作为管理员密钥（一级邀请码）验证
        // 检查是否是管理员密钥，可能是旧格式的 ak- 开头或者新格式的邮编+随机数字
        val adminKey = adminKeyService.findAdminKeyByCode(code)
        if (adminKey != null) {
            // 验证管理员密钥状态
            val adminKeyValidationResult = if (adminKey.status == AdminKeyStatus.ACTIVE) {
                AdminKeyValidationResult.Valid(adminKey)
            } else {
                AdminKeyValidationResult.Disabled
            }

            return when (adminKeyValidationResult) {
                is AdminKeyValidationResult.Valid -> {
                    // 生成JWT令牌
                    val expiresIn = 24 * 60 * 60 * 1000L // 24小时
                    val expiresAt = Date(System.currentTimeMillis() + expiresIn)

                    val token = JWT.create()
                        .withAudience(jwtAudience)
                        .withIssuer(jwtIssuer)
                        .withClaim("code", adminKeyValidationResult.adminKey.code) // 使用管理员密钥作为邀请码
                        .withClaim("adminKeyCode", adminKeyValidationResult.adminKey.code)
                        .withClaim("adminKeyId", adminKeyValidationResult.adminKey.id)
                        .withClaim("role", "admin-key") // 标记为管理员密钥角色
                        .withClaim("userType", UserType.INDIVIDUAL.toString()) // 默认为个人用户
                        .withClaim("permissionLevel", "ADMIN") // 管理员权限
                        .withExpiresAt(expiresAt)
                        .sign(Algorithm.HMAC256(jwtSecret))

                    LoginResult.AdminKeySuccess(
                        token = token,
                        expiresIn = expiresIn,
                        adminKey = adminKeyValidationResult.adminKey
                    )
                }
                AdminKeyValidationResult.NotFound -> LoginResult.CodeNotFound
                AdminKeyValidationResult.Disabled -> LoginResult.CodeInactive
            }
        }

        // 如果不是管理员密钥，则尝试作为二级邀请码验证
        val validationResult = invitationCodeService.validateInvitationCode(code)

        return when (validationResult) {
            is InvitationCodeValidationResult.Valid -> {
                // 检查并发限制
                val concurrencyLimit = validationResult.invitationCode.concurrentLimit
                val roomId = validationResult.invitationCode.code

                // 使用WebSocket连接管理器检查并发限制
                val withinLimit = runBlocking {
                    webSocketConnectionManager.checkConcurrencyLimit(roomId, concurrencyLimit)
                }

                if (!withinLimit) {
                    logger.warn("并发限制超过，邀请码: $roomId, 限制: $concurrencyLimit")
                    return LoginResult.ConcurrencyLimitExceeded
                }

                // 生成JWT令牌
                val expiresIn = 24 * 60 * 60 * 1000L // 24小时
                val expiresAt = Date(System.currentTimeMillis() + expiresIn)

                val token = JWT.create()
                    .withAudience(jwtAudience)
                    .withIssuer(jwtIssuer)
                    .withClaim("code", validationResult.invitationCode.code)
                    .withClaim("userType", validationResult.invitationCode.userType.toString())
                    .withClaim("permissionLevel", validationResult.invitationCode.permissionLevel.toString())
                    .apply {
                        // 如果有公司ID，则添加到JWT中
                        validationResult.invitationCode.companyName?.let { companyName ->
                            withClaim("companyName", companyName)
                        }
                    }
                    .withExpiresAt(expiresAt)
                    .sign(Algorithm.HMAC256(jwtSecret))

                // 不再使用Redis记录登录状态
                // WebSocket连接将在前端连接时记录

                LoginResult.Success(
                    token = token,
                    expiresIn = expiresIn,
                    invitationCode = validationResult.invitationCode
                )
            }
            InvitationCodeValidationResult.NotFound -> LoginResult.CodeNotFound
            InvitationCodeValidationResult.Inactive -> LoginResult.CodeInactive
            InvitationCodeValidationResult.Expired -> LoginResult.CodeExpired
            InvitationCodeValidationResult.InsufficientBalance -> LoginResult.InsufficientBalance
        }
    }
}