package com.dzhp.permit.services.auth

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.dzhp.permit.models.AdminKey
import com.dzhp.permit.services.AdminKeyService
import com.dzhp.permit.services.AdminKeyValidationResult
import com.dzhp.permit.services.DistrictService
import io.ktor.server.application.*
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 管理员密钥登录服务
 * 负责一级邀请码（管理员密钥）的登录验证和JWT令牌生成
 */
class AdminKeyLoginService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val districtService = DistrictService(application)
    private val adminKeyService = AdminKeyService(application, districtService)
    private val jwtSecret: String
        get() = application.environment.config.property("jwt.secret").getString()
    private val jwtIssuer: String
        get() = application.environment.config.property("jwt.domain").getString()
    private val jwtAudience: String
        get() = application.environment.config.property("jwt.audience").getString()

    /**
     * 管理员密钥登录结果
     */
    sealed class LoginResult {
        /** 登录成功 */
        data class Success(
            val token: String,
            val expiresIn: Long,
            val adminKey: AdminKey
        ) : LoginResult()

        /** 管理员密钥不存在 */
        object KeyNotFound : LoginResult()

        /** 管理员密钥已停用 */
        object KeyDisabled : LoginResult()
    }

    /**
     * 使用管理员密钥登录
     *
     * @param adminKeyCode 管理员密钥
     * @return 登录结果
     */
    fun loginWithAdminKey(adminKeyCode: String): LoginResult {
        // 验证管理员密钥
        val validationResult = adminKeyService.validateAdminKey(adminKeyCode)

        return when (validationResult) {
            is AdminKeyValidationResult.Valid -> {
                // 生成JWT令牌
                val expiresIn = 24 * 60 * 60 * 1000L // 24小时
                val expiresAt = Date(System.currentTimeMillis() + expiresIn)

                val token = JWT.create()
                    .withAudience(jwtAudience)
                    .withIssuer(jwtIssuer)
                    .withClaim("adminKeyCode", validationResult.adminKey.code)
                    .withClaim("adminKeyId", validationResult.adminKey.id)
                    .withClaim("role", "admin-key") // 标识为管理员密钥角色
                    .withClaim("maxCNYLimit", validationResult.adminKey.maxCNYLimit)
                    .withExpiresAt(expiresAt)
                    .sign(Algorithm.HMAC256(jwtSecret))

                LoginResult.Success(
                    token = token,
                    expiresIn = expiresIn,
                    adminKey = validationResult.adminKey
                )
            }
            AdminKeyValidationResult.NotFound -> LoginResult.KeyNotFound
            AdminKeyValidationResult.Disabled -> LoginResult.KeyDisabled
        }
    }
}
