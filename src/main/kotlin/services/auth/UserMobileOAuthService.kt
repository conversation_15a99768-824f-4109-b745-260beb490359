package com.dzhp.permit.services.auth

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.dzhp.permit.getMySQLDatabase
import com.dzhp.permit.models.*
import io.ktor.server.application.*
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.LoggerFactory
import java.util.Date

/**
 * 用户手机OAuth服务
 * 负责用户通过手机号进行OAuth认证的相关功能
 */
class UserMobileOAuthService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val db: Database
        get() = application.getMySQLDatabase()
    private val verificationService = UserMobileVerificationService(application)

    // JWT配置
    private val jwtSecret: String
        get() = application.environment.config.property("jwt.secret").getString()
    private val jwtIssuer: String
        get() = application.environment.config.property("jwt.domain").getString()
    private val jwtAudience: String
        get() = application.environment.config.property("jwt.audience").getString()

    /**
     * 使用手机号和验证码进行OAuth认证
     *
     * @param mobile 手机号
     * @param verificationCode 验证码
     * @return OAuth认证结果
     */
    fun authenticateWithMobileAndCode(mobile: String, verificationCode: String): OAuthResult {
        // 验证验证码
        if (!verificationService.verifyCode(mobile, verificationCode)) {
            return OAuthResult.InvalidVerificationCode
        }

        // 查找用户
        val user = findUserByMobile(mobile)
            ?: return OAuthResult.UserNotFound

        // 更新用户最后登录时间
        updateLastLoginTime(user.id)

        // 生成OAuth令牌
        val accessToken = generateAccessToken(user)
        val refreshToken = generateRefreshToken(user)

        return OAuthResult.Success(
            userId = user.id,
            accessToken = accessToken,
            refreshToken = refreshToken,
            expiresIn = 2 * 60 * 60, // 2小时，单位：秒
            tokenType = "Bearer",
            scope = "basic profile",
            userInfo = mapOf(
                "id" to user.id.toString(),
                "username" to user.username,
                "nickname" to (user.nickname ?: ""),
                "mobile" to (user.mobile ?: ""),
                "userType" to user.userType.toString(),
                "permissionLevel" to user.permissionLevel.toString()
            )
        )
    }

    /**
     * 刷新OAuth令牌
     *
     * @param refreshToken 刷新令牌
     * @return OAuth刷新结果
     */
    fun refreshToken(refreshToken: String): OAuthRefreshResult {
        try {
            // 验证刷新令牌
            val verifier = JWT.require(Algorithm.HMAC256(jwtSecret))
                .withAudience(jwtAudience)
                .withIssuer(jwtIssuer)
                .withClaim("token_type", "refresh")
                .build()

            val decodedJWT = verifier.verify(refreshToken)
            val userId = decodedJWT.getClaim("userId").asLong()

            // 查找用户
            val user = findUserById(userId)
                ?: return OAuthRefreshResult.InvalidToken

            // 生成新的访问令牌
            val newAccessToken = generateAccessToken(user)

            return OAuthRefreshResult.Success(
                accessToken = newAccessToken,
                expiresIn = 2 * 60 * 60, // 2小时，单位：秒
                tokenType = "Bearer"
            )
        } catch (e: Exception) {
            logger.error("刷新令牌失败", e)
            return OAuthRefreshResult.InvalidToken
        }
    }

    /**
     * 根据用户ID查找用户
     *
     * @param userId 用户ID
     * @return 用户信息，如果未找到则返回null
     */
    private fun findUserById(userId: Long): UserInfo? = transaction(db) {
        Users.selectAll().where { (Users.id eq userId) and (Users.deleted eq false) }
            .map {
                UserInfo(
                    id = it[Users.id],
                    username = it[Users.username],
                    nickname = it[Users.nickname],
                    realName = it[Users.realName],
                    avatar = it[Users.avatar],
                    gender = it[Users.gender],
                    birthdate = it[Users.birthdate],
                    mobile = it[Users.mobile],
                    email = it[Users.email],
                    address = it[Users.address],
                    idCardNumber = it[Users.idCardNumber],
                    companyName = it[Users.companyName],
                    departmentId = it[Users.departmentId],
                    position = it[Users.position],
                    status = it[Users.status],
                    userType = it[Users.userType],
                    permissionLevel = it[Users.permissionLevel],
                    regionConstraint = it[Users.regionConstraint],
                    preferredLanguage = it[Users.preferredLanguage],
                    timezone = it[Users.timezone],
                    notificationSettings = it[Users.notificationSettings],
                    securityLevel = it[Users.securityLevel],
                    passwordUpdatedAt = null,
                    mfaEnabled = false,
                    loginFailCount = 0,
                    lastLoginIp = null,
                    lastLoginDevice = null,
                    lastLoginAt = null,
                    registrationIp = null,
                    userLevel = it[Users.userLevel],
                    vipExpireAt = null,
                    invitationCode = null,
                    invitedBy = null,
                    tags = null,
                    remark = null,
                    createBy = it[Users.createBy],
                    updateBy = it[Users.updateBy],
                    deleted = it[Users.deleted],
                    createdAt = it[Users.createdAt],
                    updatedAt = it[Users.updatedAt],
                    version = it[Users.version]
                )
            }
            .firstOrNull()
    }

    /**
     * 根据手机号查找用户
     *
     * @param mobile 手机号
     * @return 用户信息，如果未找到则返回null
     */
    private fun findUserByMobile(mobile: String): UserInfo? = transaction(db) {
        Users.selectAll().where { (Users.mobile eq mobile) and (Users.deleted eq false) }
            .map {
                UserInfo(
                    id = it[Users.id],
                    username = it[Users.username],
                    nickname = it[Users.nickname],
                    realName = it[Users.realName],
                    avatar = it[Users.avatar],
                    gender = it[Users.gender],
                    birthdate = it[Users.birthdate],
                    mobile = it[Users.mobile],
                    email = it[Users.email],
                    address = it[Users.address],
                    idCardNumber = it[Users.idCardNumber],
                    companyName = it[Users.companyName],
                    departmentId = it[Users.departmentId],
                    position = it[Users.position],
                    status = it[Users.status],
                    userType = it[Users.userType],
                    permissionLevel = it[Users.permissionLevel],
                    regionConstraint = it[Users.regionConstraint],
                    preferredLanguage = it[Users.preferredLanguage],
                    timezone = it[Users.timezone],
                    notificationSettings = it[Users.notificationSettings],
                    securityLevel = it[Users.securityLevel],
                    passwordUpdatedAt = null,
                    mfaEnabled = false,
                    loginFailCount = 0,
                    lastLoginIp = null,
                    lastLoginDevice = null,
                    lastLoginAt = null,
                    registrationIp = null,
                    userLevel = it[Users.userLevel],
                    vipExpireAt = null,
                    invitationCode = null,
                    invitedBy = null,
                    tags = null,
                    remark = null,
                    createBy = it[Users.createBy],
                    updateBy = it[Users.updateBy],
                    deleted = it[Users.deleted],
                    createdAt = it[Users.createdAt],
                    updatedAt = it[Users.updatedAt],
                    version = it[Users.version]
                )
            }
            .firstOrNull()
    }

    /**
     * 更新用户最后登录时间
     *
     * @param userId 用户ID
     */
    private fun updateLastLoginTime(userId: Long) {
        transaction(db) {
            Users.update({ Users.id eq userId }) {
                it[lastLoginAt] = System.currentTimeMillis()
                it[updatedAt] = System.currentTimeMillis()
            }
        }
    }

    /**
     * 生成访问令牌
     *
     * @param user 用户信息
     * @return 访问令牌
     */
    private fun generateAccessToken(user: UserInfo): String {
        val expiresIn = 2 * 60 * 60 * 1000L // 2小时，单位：毫秒
        val expiresAt = Date(System.currentTimeMillis() + expiresIn)

        return JWT.create()
            .withAudience(jwtAudience)
            .withIssuer(jwtIssuer)
            .withClaim("userId", user.id)
            .withClaim("mobile", user.mobile)
            .withClaim("login_type", "mobile_oauth")
            .withClaim("userType", user.userType.toString())
            .withClaim("permissionLevel", user.permissionLevel.toString())
            .withClaim("token_type", "access")
            .withClaim("scope", "basic profile")
            .withExpiresAt(expiresAt)
            .sign(Algorithm.HMAC256(jwtSecret))
    }

    /**
     * 生成刷新令牌
     *
     * @param user 用户信息
     * @return 刷新令牌
     */
    private fun generateRefreshToken(user: UserInfo): String {
        val expiresIn = 30 * 24 * 60 * 60 * 1000L // 30天，单位：毫秒
        val expiresAt = Date(System.currentTimeMillis() + expiresIn)

        return JWT.create()
            .withAudience(jwtAudience)
            .withIssuer(jwtIssuer)
            .withClaim("userId", user.id)
            .withClaim("token_type", "refresh")
            .withExpiresAt(expiresAt)
            .sign(Algorithm.HMAC256(jwtSecret))
    }
}

/**
 * OAuth认证结果
 */
sealed class OAuthResult {
    /** 认证成功 */
    data class Success(
        val userId: Long,
        val accessToken: String,
        val refreshToken: String,
        val expiresIn: Int,
        val tokenType: String,
        val scope: String,
        val userInfo: Map<String, String>
    ) : OAuthResult()

    /** 用户不存在 */
    object UserNotFound : OAuthResult()

    /** 验证码无效 */
    object InvalidVerificationCode : OAuthResult()
}

/**
 * OAuth刷新结果
 */
sealed class OAuthRefreshResult {
    /** 刷新成功 */
    data class Success(
        val accessToken: String,
        val expiresIn: Int,
        val tokenType: String
    ) : OAuthRefreshResult()

    /** 令牌无效 */
    object InvalidToken : OAuthRefreshResult()
}
