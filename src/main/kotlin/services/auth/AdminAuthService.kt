package com.dzhp.permit.services.auth

import com.unboundid.ldap.sdk.*
import io.ktor.server.application.*
import org.slf4j.LoggerFactory
import java.security.MessageDigest
import java.sql.DriverManager
import java.sql.Connection

/**
 * LDAP配置数据类
 */
data class LdapConfig(
    val host: String,
    val port: Int,
    val bindDn: String,
    val password: String,
    val base: String
)

/**
 * 外部MySQL配置数据类
 */
data class ExternalMysqlConfig(
    val host: String,
    val port: Int,
    val user: String,
    val password: String,
    val database: String,
    val charset: String = "utf8mb4"
)

/**
 * 管理员认证结果
 */
sealed class AdminAuthResult {
    /** 认证成功 */
    data class Success(val username: String) : AdminAuthResult()
    
    /** 用户不存在 */
    object UserNotFound : AdminAuthResult()
    
    /** 密码错误 */
    object InvalidPassword : AdminAuthResult()
    
    /** 账号已停用 */
    object AccountDisabled : AdminAuthResult()
    
    /** 认证服务错误 */
    data class ServiceError(val message: String) : AdminAuthResult()
}

/**
 * 管理员认证服务
 * 支持LDAP和外部MySQL数据库两种认证方式
 */
class AdminAuthService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    
    // LDAP配置
    private val ldapConfig: LdapConfig by lazy {
        val config = application.environment.config
        LdapConfig(
            host = config.property("ldap.host").getString(),
            port = config.property("ldap.port").getString().toInt(),
            bindDn = config.property("ldap.bindDn").getString(),
            password = config.property("ldap.password").getString(),
            base = config.property("ldap.base").getString()
        )
    }
    
    // 外部MySQL配置
    private val externalMysqlConfig: ExternalMysqlConfig by lazy {
        val config = application.environment.config
        ExternalMysqlConfig(
            host = config.property("external.mysql.host").getString(),
            port = config.property("external.mysql.port").getString().toInt(),
            user = config.property("external.mysql.user").getString(),
            password = config.property("external.mysql.password").getString(),
            database = config.property("external.mysql.database").getString(),
            charset = config.propertyOrNull("external.mysql.charset")?.getString() ?: "utf8mb4"
        )
    }
    
    /**
     * 使用外部MySQL数据库进行用户认证
     * 支持用户名或手机号码登录
     */
    fun authenticateWithMysql(usernameOrPhone: String, password: String): AdminAuthResult {
        var connection: Connection? = null
        try {
            // 建立数据库连接
            val jdbcUrl = "jdbc:mysql://${externalMysqlConfig.host}:${externalMysqlConfig.port}/${externalMysqlConfig.database}?charset=${externalMysqlConfig.charset}"
            connection = DriverManager.getConnection(
                jdbcUrl,
                externalMysqlConfig.user,
                externalMysqlConfig.password
            )
            
            // 查询用户 - 支持用户名或手机号码登录
            val sql = """
                SELECT id, user_name, password, account_status, deleted
                FROM base_sys_user
                WHERE (user_name = ? OR phone = ?)
                AND deleted = 0
                LIMIT 1
            """.trimIndent()
            
            val preparedStatement = connection.prepareStatement(sql)
            preparedStatement.setString(1, usernameOrPhone)
            preparedStatement.setString(2, usernameOrPhone)
            
            val resultSet = preparedStatement.executeQuery()
            
            if (!resultSet.next()) {
                logger.warn("用户 $usernameOrPhone 登录失败：用户不存在")
                return AdminAuthResult.UserNotFound
            }
            
            val userName = resultSet.getString("user_name")
            val storedPassword = resultSet.getString("password")
            val accountStatus = resultSet.getInt("account_status")
            
            if (accountStatus != 0) {
                logger.warn("用户 $userName 登录失败：账号已停用")
                return AdminAuthResult.AccountDisabled
            }
            
            // 验证密码 - 使用SHA-256加密
            val hashedPassword = hashPassword(password)
            if (storedPassword == hashedPassword) {
                logger.info("用户 $userName 登录成功")
                return AdminAuthResult.Success(userName)
            } else {
                logger.warn("用户 $userName 登录失败：密码错误")
                return AdminAuthResult.InvalidPassword
            }
            
        } catch (e: Exception) {
            logger.error("MySQL authentication error: ${e.message}", e)
            return AdminAuthResult.ServiceError("MySQL认证服务错误: ${e.message}")
        } finally {
            connection?.close()
        }
    }
    
    /**
     * 使用LDAP进行用户认证
     */
    fun authenticateWithLdap(username: String, password: String): AdminAuthResult {
        var connection: LDAPConnection? = null
        try {
            // 建立LDAP连接
            connection = LDAPConnection(ldapConfig.host, ldapConfig.port)
            
            // 使用管理员账号绑定
            connection.bind(ldapConfig.bindDn, ldapConfig.password)
            
            // 搜索用户
            val searchFilter = Filter.createEqualityFilter("cn", username)
            val searchRequest = SearchRequest(ldapConfig.base, SearchScope.SUB, searchFilter, "cn")
            val searchResult = connection.search(searchRequest)
            
            if (searchResult.entryCount != 1) {
                logger.warn("用户 $username 登录失败：用户不存在")
                return AdminAuthResult.UserNotFound
            }
            
            val userEntry = searchResult.searchEntries[0]
            val userDn = userEntry.dn
            
            // 尝试使用用户凭据进行绑定验证
            try {
                val userConnection = LDAPConnection(ldapConfig.host, ldapConfig.port)
                userConnection.bind(userDn, password)
                userConnection.close()
                
                logger.info("用户 $username 登录成功")
                return AdminAuthResult.Success(username)
            } catch (e: LDAPBindException) {
                logger.warn("用户 $username 登录失败：密码错误")
                return AdminAuthResult.InvalidPassword
            }
            
        } catch (e: Exception) {
            logger.error("LDAP authentication error: ${e.message}", e)
            return AdminAuthResult.ServiceError("LDAP认证服务错误: ${e.message}")
        } finally {
            connection?.close()
        }
    }
    
    /**
     * 使用SHA-256对密码进行哈希
     */
    private fun hashPassword(password: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(password.toByteArray())
        return hashBytes.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * 综合认证方法
     * 先尝试MySQL认证，如果失败则尝试LDAP认证
     */
    fun authenticate(usernameOrPhone: String, password: String): AdminAuthResult {
        // 首先尝试MySQL认证
        val mysqlResult = authenticateWithMysql(usernameOrPhone, password)
        if (mysqlResult is AdminAuthResult.Success) {
            return mysqlResult
        }
        
        // 如果MySQL认证失败，尝试LDAP认证
        logger.info("MySQL认证失败，尝试LDAP认证")
        return authenticateWithLdap(usernameOrPhone, password)
    }
}
