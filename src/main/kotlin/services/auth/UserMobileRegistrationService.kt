package com.dzhp.permit.services.auth

import com.dzhp.permit.getMySQLDatabase
import com.dzhp.permit.models.*
import io.ktor.server.application.*
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.slf4j.LoggerFactory
import java.security.MessageDigest
import java.util.*

/**
 * 用户手机注册服务
 * 负责用户通过手机号注册的相关功能
 */
class UserMobileRegistrationService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val db: Database
        get() = application.getMySQLDatabase()
    private val verificationService = UserMobileVerificationService(application)

    /**
     * 使用手机号和验证码注册用户
     *
     * @param mobile 手机号
     * @param verificationCode 验证码
     * @param password 密码（可选）
     * @return 注册结果
     */
    fun registerWithMobileAndCode(
        mobile: String,
        verificationCode: String,
        password: String? = null
    ): RegistrationResult {
        // 验证验证码
        if (!verificationService.verifyCode(mobile, verificationCode)) {
            return RegistrationResult.InvalidVerificationCode
        }

        // 检查手机号是否已注册
        if (isMobileRegistered(mobile)) {
            return RegistrationResult.MobileAlreadyRegistered
        }

        // 创建用户
        return transaction(db) {
            try {
                // 创建用户基本信息
                val userId = Users.insert {
                    it[username] = generateUsername(mobile)
                    it[nickname] = "用户${mobile.takeLast(4)}"
                    it[this.mobile] = mobile
                    it[status] = UserStatus.ACTIVE
                    it[userType] = UserType.INDIVIDUAL
                    it[permissionLevel] = PermissionLevel.BASIC
                    it[securityLevel] = SecurityLevel.NORMAL
                    it[userLevel] = 1
                    it[createBy] = "system"
                    it[updateBy] = "system"
                    it[deleted] = false
                    it[createdAt] = System.currentTimeMillis()
                    it[updatedAt] = System.currentTimeMillis()
                    it[version] = 0
                } get Users.id

                // 创建用户身份认证信息（手机号）
                UserIdentities.insert {
                    it[this.userId] = userId
                    it[identityType] = IdentityType.MOBILE
                    it[identifier] = mobile
                    it[credential] = null // 手机验证码登录不需要凭证
                    it[isVerified] = true
                    it[verifiedAt] = System.currentTimeMillis()
                    it[isPrimary] = true
                    it[priority] = 100
                    it[createBy] = "system"
                    it[updateBy] = "system"
                    it[deleted] = false
                    it[createdAt] = System.currentTimeMillis()
                    it[updatedAt] = System.currentTimeMillis()
                    it[version] = 0
                }

                // 如果提供了密码，创建用户名密码身份
                if (!password.isNullOrEmpty()) {
                    UserIdentities.insert {
                        it[this.userId] = userId
                        it[identityType] = IdentityType.USERNAME
                        it[identifier] = mobile // 使用手机号作为用户名
                        it[credential] = hashPassword(password)
                        it[isVerified] = true
                        it[verifiedAt] = System.currentTimeMillis()
                        it[isPrimary] = false
                        it[priority] = 50
                        it[createBy] = "system"
                        it[updateBy] = "system"
                        it[deleted] = false
                        it[createdAt] = System.currentTimeMillis()
                        it[updatedAt] = System.currentTimeMillis()
                        it[version] = 0
                    }
                }

                // 创建用户账户
                createUserAccounts(userId)

                RegistrationResult.Success(userId)
            } catch (e: Exception) {
                logger.error("用户注册失败", e)
                rollback()
                RegistrationResult.SystemError(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 检查手机号是否已注册
     *
     * @param mobile 手机号
     * @return 是否已注册
     */
    private fun isMobileRegistered(mobile: String): Boolean = transaction(db) {
        Users.selectAll().where { (Users.mobile eq mobile) and (Users.deleted eq false) }
            .count() > 0
    }

    /**
     * 为用户创建账户
     *
     * @param userId 用户ID
     */
    private fun createUserAccounts(userId: Long) {
        // 创建主账户
        UserAccounts.insert {
            it[this.userId] = userId
            it[accountType] = AccountType.MAIN
            it[balance] = 0.0
            it[frozenAmount] = 0.0
            it[totalRecharge] = 0.0
            it[totalConsumption] = 0.0
            it[totalGift] = 0.0
            it[currency] = "CNY"
            it[status] = AccountStatus.NORMAL
            it[createBy] = "system"
            it[updateBy] = "system"
            it[deleted] = false
            it[createdAt] = System.currentTimeMillis()
            it[updatedAt] = System.currentTimeMillis()
            it[version] = 0
        }

        // 创建赠送账户
        UserAccounts.insert {
            it[this.userId] = userId
            it[accountType] = AccountType.GIFT
            it[balance] = 0.0
            it[frozenAmount] = 0.0
            it[totalRecharge] = 0.0
            it[totalConsumption] = 0.0
            it[totalGift] = 0.0
            it[currency] = "CNY"
            it[status] = AccountStatus.NORMAL
            it[createBy] = "system"
            it[updateBy] = "system"
            it[deleted] = false
            it[createdAt] = System.currentTimeMillis()
            it[updatedAt] = System.currentTimeMillis()
            it[version] = 0
        }
    }

    /**
     * 生成用户名
     *
     * @param mobile 手机号
     * @return 生成的用户名
     */
    private fun generateUsername(mobile: String): String {
        return "user_${mobile.takeLast(4)}_${System.currentTimeMillis() % 10000}"
    }

    /**
     * 对密码进行哈希处理
     *
     * @param password 原始密码
     * @return 哈希后的密码
     */
    private fun hashPassword(password: String): String {
        val md = MessageDigest.getInstance("SHA-256")
        val hash = md.digest(password.toByteArray())
        return Base64.getEncoder().encodeToString(hash)
    }
}

/**
 * 注册结果
 */
sealed class RegistrationResult {
    /** 注册成功 */
    data class Success(val userId: Long) : RegistrationResult()

    /** 验证码无效 */
    object InvalidVerificationCode : RegistrationResult()

    /** 手机号已注册 */
    object MobileAlreadyRegistered : RegistrationResult()

    /** 系统错误 */
    data class SystemError(val message: String) : RegistrationResult()
}
