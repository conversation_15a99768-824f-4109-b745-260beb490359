package com.dzhp.permit.services.auth

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.dzhp.permit.getWebSocketConnectionManager
import com.dzhp.permit.models.UserType
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 微信登录服务
 * 负责处理微信OAuth2.0授权登录流程
 */
class UserWeChatLoginService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val webSocketConnectionManager = application.getWebSocketConnectionManager()

    // 从配置文件获取微信应用配置
    private val appId: String
        get() = application.environment.config.propertyOrNull("wechat.appId")?.getString() ?: ""
    private val appSecret: String
        get() = application.environment.config.propertyOrNull("wechat.appSecret")?.getString() ?: ""

    // JWT配置
    private val jwtSecret: String
        get() = application.environment.config.property("jwt.secret").getString()
    private val jwtIssuer: String
        get() = application.environment.config.property("jwt.domain").getString()
    private val jwtAudience: String
        get() = application.environment.config.property("jwt.audience").getString()

    // HTTP客户端
    private val httpClient = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
            }, ContentType.Application.Json)
        }
    }

    /**
     * 微信授权登录结果
     */
    sealed class WeChatLoginResult {
        /** 登录成功 */
        data class Success(
            val token: String,
            val expiresIn: Long,
            val openId: String,
            val unionId: String?,
            val userType: UserType = UserType.INDIVIDUAL,
            val permissionLevel: String = "BASIC"
        ) : WeChatLoginResult()

        /** 授权码无效 */
        object InvalidCode : WeChatLoginResult()

        /** 微信API调用失败 */
        data class ApiError(val errorCode: Int, val errorMsg: String) : WeChatLoginResult()

        /** 服务器内部错误 */
        data class ServerError(val message: String) : WeChatLoginResult()
    }

    /**
     * 微信授权接口返回数据
     */
    @Serializable
    private data class WeChatAccessTokenResponse(
        val access_token: String? = null,
        val expires_in: Int? = null,
        val refresh_token: String? = null,
        val openid: String? = null,
        val scope: String? = null,
        val unionid: String? = null,
        val errcode: Int? = null,
        val errmsg: String? = null
    )

    /**
     * 使用授权码登录
     *
     * @param code 微信授权临时票据
     * @return 登录结果
     */
    suspend fun loginWithCode(code: String): WeChatLoginResult {
        try {
            if (appId.isEmpty() || appSecret.isEmpty()) {
                logger.error("微信应用配置缺失，请检查配置文件")
                return WeChatLoginResult.ServerError("微信应用配置缺失")
            }

            // 通过code获取access_token
            val tokenResponse = getAccessToken(code)

            // 检查是否有错误
            if (tokenResponse.errcode != null && tokenResponse.errcode != 0) {
                logger.warn("微信授权失败: ${tokenResponse.errcode} - ${tokenResponse.errmsg}")
                return WeChatLoginResult.ApiError(
                    tokenResponse.errcode,
                    tokenResponse.errmsg ?: "未知错误"
                )
            }

            // 验证必要字段
            val accessToken = tokenResponse.access_token
            val openId = tokenResponse.openid
            val expiresIn = tokenResponse.expires_in

            if (accessToken.isNullOrEmpty() || openId.isNullOrEmpty() || expiresIn == null) {
                logger.warn("微信授权返回数据不完整")
                return WeChatLoginResult.InvalidCode
            }

            // 生成JWT令牌
            val jwtExpiresIn = 24 * 60 * 60 * 1000L // 24小时
            val jwtExpiresAt = Date(System.currentTimeMillis() + jwtExpiresIn)

            val token = JWT.create()
                .withAudience(jwtAudience)
                .withIssuer(jwtIssuer)
                .withClaim("openid", openId)
                .withClaim("unionid", tokenResponse.unionid)
                .withClaim("login_type", "wechat")
                .withClaim("userType", UserType.INDIVIDUAL.toString())
                .withClaim("permissionLevel", "BASIC")
                .withExpiresAt(jwtExpiresAt)
                .sign(Algorithm.HMAC256(jwtSecret))

            // 检查并发限制 - 微信登录默认使用openId作为房间ID
            val withinLimit = runBlocking {
                // 微信登录默认并发数为1
                webSocketConnectionManager.checkConcurrencyLimit(openId, 1)
            }

            if (!withinLimit) {
                logger.warn("并发限制超过，微信用户: $openId")
                // 对于微信登录，我们仍然允许登录，但会关闭之前的连接
                runBlocking {
                    // 这里可以实现关闭之前连接的逻辑
                }
            }

            return WeChatLoginResult.Success(
                token = token,
                expiresIn = jwtExpiresIn,
                openId = openId,
                unionId = tokenResponse.unionid,
                userType = UserType.INDIVIDUAL,
                permissionLevel = "BASIC"
            )

        } catch (e: Exception) {
            logger.error("微信登录处理异常: ${e.message}", e)
            return WeChatLoginResult.ServerError(e.message ?: "未知错误")
        }
    }

    /**
     * 通过授权码获取访问令牌
     *
     * @param code 授权码
     * @return 访问令牌响应
     */
    private suspend fun getAccessToken(code: String): WeChatAccessTokenResponse {
        val url = "https://api.weixin.qq.com/sns/oauth2/access_token"

        return httpClient.get(url) {
            parameter("appid", appId)
            parameter("secret", appSecret)
            parameter("code", code)
            parameter("grant_type", "authorization_code")
        }.body()
    }

    /**
     * 关闭资源
     */
    fun close() {
        httpClient.close()
    }
}
