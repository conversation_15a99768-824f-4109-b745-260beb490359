package com.dzhp.permit.services.auth

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.dzhp.permit.getMySQLDatabase
import com.dzhp.permit.models.*
import io.ktor.server.application.*
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.slf4j.LoggerFactory
import java.security.MessageDigest
import java.util.*
import java.util.Date

/**
 * 用户手机登录服务
 * 负责用户通过手机号登录的相关功能
 */
class UserMobileLoginService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val db: Database
        get() = application.getMySQLDatabase()
    private val verificationService = UserMobileVerificationService(application)

    // JWT配置
    private val jwtSecret: String
        get() = application.environment.config.property("jwt.secret").getString()
    private val jwtIssuer: String
        get() = application.environment.config.property("jwt.domain").getString()
    private val jwtAudience: String
        get() = application.environment.config.property("jwt.audience").getString()

    /**
     * 使用手机号和验证码登录
     *
     * @param mobile 手机号
     * @param verificationCode 验证码
     * @return 登录结果
     */
    fun loginWithMobileAndCode(mobile: String, verificationCode: String): LoginResult {
        // 验证验证码
        if (!verificationService.verifyCode(mobile, verificationCode)) {
            return LoginResult.InvalidVerificationCode
        }

        // 查找用户
        val user = findUserByMobile(mobile)
            ?: return LoginResult.UserNotFound

        // 更新用户最后登录时间
        updateLastLoginTime(user.id)

        // 生成JWT令牌
        val token = generateJwtToken(user)

        return LoginResult.Success(
            userId = user.id,
            token = token,
            expiresIn = 24 * 60 * 60 * 1000L, // 24小时
            userType = user.userType,
            permissionLevel = user.permissionLevel.toString()
        )
    }

    /**
     * 使用手机号和密码登录
     *
     * @param mobile 手机号
     * @param password 密码
     * @return 登录结果
     */
    fun loginWithMobileAndPassword(mobile: String, password: String): LoginResult {
        // 查找用户
        val user = findUserByMobile(mobile)
            ?: return LoginResult.UserNotFound

        // 验证密码
        if (!verifyPassword(user.id, mobile, password)) {
            return LoginResult.InvalidCredentials
        }

        // 更新用户最后登录时间
        updateLastLoginTime(user.id)

        // 生成JWT令牌
        val token = generateJwtToken(user)

        return LoginResult.Success(
            userId = user.id,
            token = token,
            expiresIn = 24 * 60 * 60 * 1000L, // 24小时
            userType = user.userType,
            permissionLevel = user.permissionLevel.toString()
        )
    }

    /**
     * 根据手机号查找用户
     *
     * @param mobile 手机号
     * @return 用户信息，如果未找到则返回null
     */
    private fun findUserByMobile(mobile: String): UserInfo? = transaction(db) {
        Users.selectAll().where { (Users.mobile eq mobile) and (Users.deleted eq false) }
            .map {
                UserInfo(
                    id = it[Users.id],
                    username = it[Users.username],
                    nickname = it[Users.nickname],
                    realName = it[Users.realName],
                    avatar = it[Users.avatar],
                    gender = it[Users.gender],
                    birthdate = it[Users.birthdate],
                    mobile = it[Users.mobile],
                    email = it[Users.email],
                    address = it[Users.address],
                    idCardNumber = it[Users.idCardNumber],
                    companyName = it[Users.companyName],
                    departmentId = it[Users.departmentId],
                    position = it[Users.position],
                    status = it[Users.status],
                    userType = it[Users.userType],
                    permissionLevel = it[Users.permissionLevel],
                    regionConstraint = it[Users.regionConstraint],
                    preferredLanguage = it[Users.preferredLanguage],
                    timezone = it[Users.timezone],
                    notificationSettings = it[Users.notificationSettings],
                    securityLevel = it[Users.securityLevel],
                    passwordUpdatedAt = null,
                    mfaEnabled = false,
                    loginFailCount = 0,
                    lastLoginIp = null,
                    lastLoginDevice = null,
                    lastLoginAt = null,
                    registrationIp = null,
                    userLevel = it[Users.userLevel],
                    vipExpireAt = null,
                    invitationCode = null,
                    invitedBy = null,
                    tags = null,
                    remark = null,
                    createBy = it[Users.createBy],
                    updateBy = it[Users.updateBy],
                    deleted = it[Users.deleted],
                    createdAt = it[Users.createdAt],
                    updatedAt = it[Users.updatedAt],
                    version = it[Users.version]
                )
            }
            .firstOrNull()
    }

    /**
     * 验证密码
     *
     * @param userId 用户ID
     * @param mobile 手机号（用作用户名）
     * @param password 密码
     * @return 密码是否正确
     */
    private fun verifyPassword(userId: Long, mobile: String, password: String): Boolean = transaction(db) {
        val identity = UserIdentities.selectAll().where {
            (UserIdentities.userId eq userId) and
            (UserIdentities.identityType eq IdentityType.USERNAME) and
            (UserIdentities.identifier eq mobile) and
            (UserIdentities.deleted eq false)
        }.firstOrNull() ?: return@transaction false

        val storedHash = identity[UserIdentities.credential]
        val inputHash = hashPassword(password)

        storedHash == inputHash
    }

    /**
     * 更新用户最后登录时间
     *
     * @param userId 用户ID
     */
    private fun updateLastLoginTime(userId: Long) {
        transaction(db) {
            Users.update({ Users.id eq userId }) {
                it[lastLoginAt] = System.currentTimeMillis()
                it[updatedAt] = System.currentTimeMillis()
            }
        }
    }

    /**
     * 生成JWT令牌
     *
     * @param user 用户信息
     * @return JWT令牌
     */
    private fun generateJwtToken(user: UserInfo): String {
        val expiresIn = 24 * 60 * 60 * 1000L // 24小时
        val expiresAt = Date(System.currentTimeMillis() + expiresIn)

        return JWT.create()
            .withAudience(jwtAudience)
            .withIssuer(jwtIssuer)
            .withClaim("userId", user.id)
            .withClaim("mobile", user.mobile)
            .withClaim("login_type", "mobile")
            .withClaim("userType", user.userType.toString())
            .withClaim("permissionLevel", user.permissionLevel.toString())
            .withExpiresAt(expiresAt)
            .sign(Algorithm.HMAC256(jwtSecret))
    }

    /**
     * 对密码进行哈希处理
     *
     * @param password 原始密码
     * @return 哈希后的密码
     */
    private fun hashPassword(password: String): String {
        val md = MessageDigest.getInstance("SHA-256")
        val hash = md.digest(password.toByteArray())
        return Base64.getEncoder().encodeToString(hash)
    }
}

/**
 * 登录结果
 */
sealed class LoginResult {
    /** 登录成功 */
    data class Success(
        val userId: Long,
        val token: String,
        val expiresIn: Long,
        val userType: UserType,
        val permissionLevel: String
    ) : LoginResult()

    /** 用户不存在 */
    object UserNotFound : LoginResult()

    /** 验证码无效 */
    object InvalidVerificationCode : LoginResult()

    /** 凭证无效（密码错误） */
    object InvalidCredentials : LoginResult()
}
