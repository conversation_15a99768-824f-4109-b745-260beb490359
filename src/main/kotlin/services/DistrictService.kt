package com.dzhp.permit.services

import com.google.gson.Gson
import io.ktor.server.application.*
import org.elasticsearch.action.search.SearchRequest
import org.elasticsearch.client.RequestOptions
import org.elasticsearch.client.RestHighLevelClient
import org.elasticsearch.index.query.QueryBuilders
import org.elasticsearch.index.query.functionscore.RandomScoreFunctionBuilder
import org.elasticsearch.search.builder.SearchSourceBuilder
import org.slf4j.LoggerFactory
import com.dzhp.permit.getElasticSearchClient
import org.elasticsearch.index.query.BoolQueryBuilder

/**
 * 地区服务
 * 提供与地区信息相关的功能，包括获取随机地区邮编等
 */
class DistrictService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val gson = Gson()
    private val client: RestHighLevelClient
        get() = application.getElasticSearchClient()

    companion object {
        private const val DISTRICT_INDEX = "china_province_district_index_without_geometry"
    }

    /**
     * 获取随机地区信息
     * 从Elasticsearch中随机获取一个地区信息
     *
     * @return 地区信息，包含id(邮编)、省市区等信息
     */
    fun getRandomDistrict(): DistrictInfo? {
        try {
            // 构建随机查询
            val functionScoreQuery = QueryBuilders.functionScoreQuery(
                QueryBuilders.matchAllQuery(),
                RandomScoreFunctionBuilder()
            )

            val searchSourceBuilder = SearchSourceBuilder()
                .query(functionScoreQuery)
                .size(1)

            // 创建搜索请求
            val searchRequest = SearchRequest(DISTRICT_INDEX)
                .source(searchSourceBuilder)

            // 执行搜索
            val response = client.search(searchRequest, RequestOptions.DEFAULT)

            // 处理结果
            if ((response.hits.totalHits?.value ?: 0) > 0 && response.hits.hits.isNotEmpty()) {
                val hit = response.hits.hits[0]
                val source = hit.sourceAsMap

                return DistrictInfo(
                    id = source["id"]?.toString() ?: "",
                    province = source["zjjcfz_province"]?.toString() ?: "",
                    city = source["zjjcfz_city"]?.toString() ?: "",
                    district = source["zjjcfz_district"]?.toString() ?: "",
                    districtShortName = source["zjjcfz_district_short_name"]?.toString() ?: "",
                    extPath = source["ext_path"]?.toString() ?: ""
                )
            }
        } catch (e: Exception) {
            logger.error("Error getting random district: ${e.message}", e)
        }

        return null
    }

    /**
     * 根据省市区查询地区信息
     * 从Elasticsearch中查询指定省市区的地区信息
     *
     * @param province 省份名称
     * @param city 城市名称
     * @param district 区县名称
     * @return 地区信息列表
     */
    fun searchDistrictByRegion(province: String, city: String, district: String): List<DistrictInfo> {
        try {
            // 构建布尔查询
            val boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("zjjcfz_province.keyword", province))
                .must(QueryBuilders.termQuery("zjjcfz_city.keyword", city))
                .must(QueryBuilders.termQuery("zjjcfz_district.keyword", district))

            val searchSourceBuilder = SearchSourceBuilder()
                .query(boolQuery)
                .size(10) // 限制返回数量，通常一个省市区组合应该只有一条记录

            // 创建搜索请求
            val searchRequest = SearchRequest(DISTRICT_INDEX)
                .source(searchSourceBuilder)

            // 执行搜索
            val response = client.search(searchRequest, RequestOptions.DEFAULT)

            // 处理结果
            val results = mutableListOf<DistrictInfo>()
            if ((response.hits.totalHits?.value ?: 0) > 0) {
                response.hits.hits.forEach { hit ->
                    val source = hit.sourceAsMap

                    results.add(DistrictInfo(
                        id = source["id"]?.toString() ?: "",
                        province = source["zjjcfz_province"]?.toString() ?: "",
                        city = source["zjjcfz_city"]?.toString() ?: "",
                        district = source["zjjcfz_district"]?.toString() ?: "",
                        districtShortName = source["zjjcfz_district_short_name"]?.toString() ?: "",
                        extPath = source["ext_path"]?.toString() ?: ""
                    ))
                }
            }

            return results
        } catch (e: Exception) {
            logger.error("Error searching district by region: ${e.message}", e)
            return emptyList()
        }
    }

    /**
     * 获取指定省市的默认区县
     * 从 Elasticsearch 中查询指定省市的区县列表，并返回第一个作为默认区县
     *
     * @param province 省份名称
     * @param city 城市名称
     * @return 区县信息列表
     */
    fun getDefaultDistrictForCity(province: String, city: String): List<DistrictInfo> {
        try {
            // 构建布尔查询，只查询省和市
            val boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("zjjcfz_province.keyword", province))
                .must(QueryBuilders.termQuery("zjjcfz_city.keyword", city))

            val searchSourceBuilder = SearchSourceBuilder()
                .query(boolQuery)
                .size(1) // 只返回第一个作为默认区县

            // 创建搜索请求
            val searchRequest = SearchRequest(DISTRICT_INDEX)
                .source(searchSourceBuilder)

            // 执行搜索
            val response = client.search(searchRequest, RequestOptions.DEFAULT)

            // 处理结果
            val results = mutableListOf<DistrictInfo>()
            if ((response.hits.totalHits?.value ?: 0) > 0) {
                response.hits.hits.forEach { hit ->
                    val source = hit.sourceAsMap

                    results.add(DistrictInfo(
                        id = source["id"]?.toString() ?: "",
                        province = source["zjjcfz_province"]?.toString() ?: "",
                        city = source["zjjcfz_city"]?.toString() ?: "",
                        district = source["zjjcfz_district"]?.toString() ?: "",
                        districtShortName = source["zjjcfz_district_short_name"]?.toString() ?: "",
                        extPath = source["ext_path"]?.toString() ?: ""
                    ))
                }
            }

            return results
        } catch (e: Exception) {
            logger.error("Error getting default district for city: ${e.message}", e)
            return emptyList()
        }
    }
}

/**
 * 地区信息数据类
 */
data class DistrictInfo(
    val id: String,                 // 地区编码（邮编）
    val province: String,           // 省份
    val city: String,               // 城市
    val district: String,           // 区县
    val districtShortName: String,  // 区县简称
    val extPath: String             // 完整路径
)
