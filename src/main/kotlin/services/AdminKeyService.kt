package com.dzhp.permit.services

import com.dzhp.permit.getMySQLDatabase
import com.dzhp.permit.models.*
import io.ktor.server.application.*
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.LoggerFactory
import java.security.SecureRandom
import java.util.*
import kotlin.random.Random

/**
 * 管理员密钥服务
 * 负责管理员密钥的创建、查询和管理
 */
class AdminKeyService(
    private val application: Application,
    private val districtService: DistrictService
) {
    private val db: Database
        get() = application.getMySQLDatabase()

    /**
     * 创建新的管理员密钥
     *
     * @param adminKey 待创建的管理员密钥信息
     * @return 数据库生成的主键 ID
     */
    fun createAdminKey(adminKey: AdminKey): Long =
        transaction(db) {
            AdminKeys.insert {
                it[code] = adminKey.code
                it[maxCNYLimit] = adminKey.maxCNYLimit
                it[status] = adminKey.status
                it[accountType] = adminKey.accountType

                // 地域信息
                it[province] = adminKey.province
                it[city] = adminKey.city
                it[district] = adminKey.district

                // 单位和联系信息
                it[companyName] = adminKey.companyName
                it[contractNumber] = adminKey.contractNumber
                it[contactPerson] = adminKey.contactPerson
                it[contactPhone] = adminKey.contactPhone
                it[contactEmail] = adminKey.contactEmail

                // 有效期
                it[expiresAt] = adminKey.expiresAt

                it[createBy] = adminKey.createBy
                it[updateBy] = adminKey.updateBy
                it[deleted] = adminKey.deleted
                it[createdAt] = adminKey.createdAt
                it[updatedAt] = adminKey.updatedAt
            } get AdminKeys.id
        }

    /**
     * 获取所有管理员密钥列表
     *
     * @param onlyActive 是否只返回有效的管理员密钥，默认为 true
     * @return 管理员密钥列表
     */
    fun listAdminKeys(onlyActive: Boolean = true): List<AdminKey> =
        transaction(db) {
            val query = if (onlyActive) {
                AdminKeys.selectAll().where { (AdminKeys.status eq AdminKeyStatus.ACTIVE) and (AdminKeys.deleted eq false) }
            } else {
                AdminKeys.selectAll().where { AdminKeys.deleted eq false }
            }

            query.map { row ->
                AdminKey(
                    id = row[AdminKeys.id],
                    code = row[AdminKeys.code],
                    maxCNYLimit = row[AdminKeys.maxCNYLimit],
                    status = row[AdminKeys.status],
                    accountType = row[AdminKeys.accountType],

                    // 地域信息
                    province = row[AdminKeys.province],
                    city = row[AdminKeys.city],
                    district = row[AdminKeys.district],

                    // 单位和联系信息
                    companyName = row[AdminKeys.companyName],
                    contractNumber = row[AdminKeys.contractNumber],
                    contactPerson = row[AdminKeys.contactPerson],
                    contactPhone = row[AdminKeys.contactPhone],
                    contactEmail = row[AdminKeys.contactEmail],

                    // 有效期
                    expiresAt = row[AdminKeys.expiresAt],

                    createBy = row[AdminKeys.createBy],
                    updateBy = row[AdminKeys.updateBy],
                    deleted = row[AdminKeys.deleted],
                    createdAt = row[AdminKeys.createdAt],
                    updatedAt = row[AdminKeys.updatedAt]
                )
            }
        }

    /**
     * 根据管理员密钥字符串查询管理员密钥
     *
     * @param code 管理员密钥字符串
     * @return 管理员密钥对象，如果不存在则返回 null
     */
    fun findAdminKeyByCode(code: String): AdminKey? =
        transaction(db) {
            AdminKeys.selectAll().where {
                (AdminKeys.code eq code) and (AdminKeys.deleted eq false)
            }.map { row ->
                AdminKey(
                    id = row[AdminKeys.id],
                    code = row[AdminKeys.code],
                    maxCNYLimit = row[AdminKeys.maxCNYLimit],
                    status = row[AdminKeys.status],
                    accountType = row[AdminKeys.accountType],

                    // 地域信息
                    province = row[AdminKeys.province],
                    city = row[AdminKeys.city],
                    district = row[AdminKeys.district],

                    // 单位和联系信息
                    companyName = row[AdminKeys.companyName],
                    contractNumber = row[AdminKeys.contractNumber],
                    contactPerson = row[AdminKeys.contactPerson],
                    contactPhone = row[AdminKeys.contactPhone],
                    contactEmail = row[AdminKeys.contactEmail],

                    // 有效期
                    expiresAt = row[AdminKeys.expiresAt],

                    createBy = row[AdminKeys.createBy],
                    updateBy = row[AdminKeys.updateBy],
                    deleted = row[AdminKeys.deleted],
                    createdAt = row[AdminKeys.createdAt],
                    updatedAt = row[AdminKeys.updatedAt]
                )
            }.singleOrNull()
        }

    /**
     * 根据ID查询管理员密钥
     *
     * @param id 管理员密钥ID
     * @return 管理员密钥对象，如果不存在则返回 null
     */
    fun findAdminKeyById(id: Long): AdminKey? =
        transaction(db) {
            AdminKeys.selectAll().where {
                (AdminKeys.id eq id) and (AdminKeys.deleted eq false)
            }.map { row ->
                AdminKey(
                    id = row[AdminKeys.id],
                    code = row[AdminKeys.code],
                    maxCNYLimit = row[AdminKeys.maxCNYLimit],
                    status = row[AdminKeys.status],
                    accountType = row[AdminKeys.accountType],

                    // 地域信息
                    province = row[AdminKeys.province],
                    city = row[AdminKeys.city],
                    district = row[AdminKeys.district],

                    // 单位和联系信息
                    companyName = row[AdminKeys.companyName],
                    contractNumber = row[AdminKeys.contractNumber],
                    contactPerson = row[AdminKeys.contactPerson],
                    contactPhone = row[AdminKeys.contactPhone],
                    contactEmail = row[AdminKeys.contactEmail],

                    // 有效期
                    expiresAt = row[AdminKeys.expiresAt],

                    createBy = row[AdminKeys.createBy],
                    updateBy = row[AdminKeys.updateBy],
                    deleted = row[AdminKeys.deleted],
                    createdAt = row[AdminKeys.createdAt],
                    updatedAt = row[AdminKeys.updatedAt]
                )
            }.singleOrNull()
        }

    /**
     * 生成管理员密钥
     * 格式: 地区邮编（6位）+ 随机数字（4位）
     * 例如：317100 9876
     *
     * @param province 省份名称
     * @param city 城市名称
     * @param district 区县名称
     * @return 生成的管理员密钥
     */
    fun generateRandomAdminKey(province: String, city: String, district: String): String {
        // 使用用户提供的省市区信息查询地区
        val districts = districtService.searchDistrictByRegion(province, city, district)
        val districtInfo = districts.firstOrNull()

        // 如果无法获取地区信息，使用默认的邮编
        val postalCode = districtInfo?.id ?: "100000"

        // 生成随机4位数字
        val randomDigits = Random.nextInt(1000, 10000).toString()

        // 组合邮编和随机数字
        return "$postalCode$randomDigits"
    }

    /**
     * 生成二级邀请码
     * 格式: 地区邮编（6位）+ 随机数字（8位）
     * 例如：20000012345678
     *
     * @param province 省份名称
     * @param city 城市名称
     * @param district 区县名称
     * @return 生成的二级邀请码
     */
    fun generateSecondaryInvitationCode(province: String, city: String, district: String): String {
        // 使用用户提供的省市区信息查询地区
        val districts = districtService.searchDistrictByRegion(province, city, district)
        val districtInfo = districts.firstOrNull()
        val postalCode = districtInfo?.id ?: "200000" // 默认使用上海市邮编

        // 随机生成8位数字
        val randomDigits = Random.nextInt(10000000, 100000000).toString()

        // 组合邮编和随机数字
        return "$postalCode$randomDigits"
    }

    /**
     * 验证管理员密钥
     *
     * @param code 管理员密钥字符串
     * @return 验证结果
     */
    fun validateAdminKey(code: String): AdminKeyValidationResult {
        val adminKey = findAdminKeyByCode(code)

        return when {
            adminKey == null -> AdminKeyValidationResult.NotFound
            adminKey.status == AdminKeyStatus.DISABLED -> AdminKeyValidationResult.Disabled
            else -> AdminKeyValidationResult.Valid(adminKey)
        }
    }

    /**
     * 更新管理员密钥状态
     *
     * @param id 管理员密钥ID
     * @param status 新状态
     * @param updateBy 更新人
     * @return 是否更新成功
     */
    fun updateAdminKeyStatus(id: Long, status: AdminKeyStatus, updateBy: String? = null): Boolean =
        transaction(db) {
            val updatedRows = AdminKeys.update({ (AdminKeys.id eq id) and (AdminKeys.deleted eq false) }) {
                it[AdminKeys.status] = status
                it[AdminKeys.updateBy] = updateBy
                it[AdminKeys.updatedAt] = System.currentTimeMillis()
            }
            updatedRows > 0
        }

    /**
     * 软删除管理员密钥
     *
     * @param id 管理员密钥ID
     * @param updateBy 更新人
     * @return 是否删除成功
     */
    fun deleteAdminKey(id: Long, updateBy: String? = null): Boolean =
        transaction(db) {
            val updatedRows = AdminKeys.update({ (AdminKeys.id eq id) and (AdminKeys.deleted eq false) }) {
                it[AdminKeys.deleted] = true
                it[AdminKeys.updateBy] = updateBy
                it[AdminKeys.updatedAt] = System.currentTimeMillis()
            }
            updatedRows > 0
        }

    /**
     * 更新管理员密钥信息
     * 不允许修改的字段：code, expiresAt, province, city, district, consumedAmount
     *
     * @param id 管理员密钥ID
     * @param updates 要更新的管理员密钥信息
     * @param updateBy 更新人
     * @return 是否更新成功
     */
    fun updateAdminKey(id: Long, updates: AdminKey, updateBy: String? = null): Boolean =
        transaction(db) {
            // 首先获取当前的管理员密钥信息，以保留不可修改的字段
            val currentAdminKey = findAdminKeyById(id)
            if (currentAdminKey == null) {
                return@transaction false
            }

            val updatedRows = AdminKeys.update({ (AdminKeys.id eq id) and (AdminKeys.deleted eq false) }) {
                // 不允许修改的字段：code, expiresAt, province, city, district
                // 这些字段保持原值

                // 可以修改的字段
                it[maxCNYLimit] = updates.maxCNYLimit
                it[status] = updates.status
                it[accountType] = updates.accountType

                // 单位和联系信息
                it[companyName] = updates.companyName
                it[contractNumber] = updates.contractNumber
                it[contactPerson] = updates.contactPerson
                it[contactPhone] = updates.contactPhone
                it[contactEmail] = updates.contactEmail

                // 更新人和更新时间
                it[AdminKeys.updateBy] = updateBy ?: updates.updateBy
                it[AdminKeys.updatedAt] = System.currentTimeMillis()
            }
            updatedRows > 0
        }
}

/**
 * 管理员密钥验证结果
 */
sealed class AdminKeyValidationResult {
    /** 有效的管理员密钥 */
    data class Valid(val adminKey: AdminKey) : AdminKeyValidationResult()

    /** 管理员密钥不存在 */
    object NotFound : AdminKeyValidationResult()

    /** 管理员密钥已停用 */
    object Disabled : AdminKeyValidationResult()
}
