package com.dzhp.permit.services

import io.ktor.server.application.*
import com.dzhp.permit.getRedisConnection
import com.dzhp.permit.models.InvitationCode

/**
 * 邀请码Redis服务
 * 负责管理邀请码的登录状态和并发控制
 */
class InvitationCodeRedisService(
    private val application: Application
) {
    companion object {
        // Redis键前缀，用于区分不同类型的数据
        private const val INVITATION_CODE_LOGIN_PREFIX = "invitation:login:"

        // 登录会话的默认过期时间（24小时，单位：秒）
        private const val DEFAULT_SESSION_EXPIRY = 24 * 60 * 60
    }

    /**
     * 检查邀请码的并发登录数量是否已达到限制
     *
     * @param invitationCode 邀请码对象
     * @return 如果未达到并发限制则返回true，否则返回false
     */
    fun checkConcurrencyLimit(invitationCode: InvitationCode): Boolean {
        application.getRedisConnection().use { jedis ->
            val key = getLoginCountKey(invitationCode.code)
            val currentCount = jedis.scard(key)

            return currentCount < invitationCode.concurrentLimit
        }
    }

    /**
     * 记录新的登录会话
     *
     * @param invitationCode 邀请码对象
     * @param sessionId 会话ID，通常是JWT令牌或其他唯一标识符
     * @return 操作是否成功
     */
    fun recordLogin(invitationCode: InvitationCode, sessionId: String): Boolean {
        application.getRedisConnection().use { jedis ->
            val key = getLoginCountKey(invitationCode.code)

            // 添加会话ID到集合中
            val result = jedis.sadd(key, sessionId)

            // 设置键的过期时间（如果尚未设置）
            if (jedis.ttl(key) < 0) {
                jedis.expire(key, DEFAULT_SESSION_EXPIRY.toLong())
            }

            return result > 0
        }
    }

    /**
     * 移除登录会话
     *
     * @param invitationCode 邀请码
     * @param sessionId 会话ID
     * @return 操作是否成功
     */
    fun removeLogin(invitationCode: String, sessionId: String): Boolean {
        application.getRedisConnection().use { jedis ->
            val key = getLoginCountKey(invitationCode)
            val result = jedis.srem(key, sessionId)
            return result > 0
        }
    }

    /**
     * 获取当前邀请码的活跃登录数量
     *
     * @param invitationCode 邀请码
     * @return 当前活跃的登录数量
     */
    fun getActiveLoginCount(invitationCode: String): Long {
        application.getRedisConnection().use { jedis ->
            val key = getLoginCountKey(invitationCode)
            return jedis.scard(key)
        }
    }

    /**
     * 获取邀请码登录计数的Redis键
     *
     * @param invitationCode 邀请码
     * @return Redis键
     */
    private fun getLoginCountKey(invitationCode: String): String {
        return "$INVITATION_CODE_LOGIN_PREFIX$invitationCode"
    }
}
