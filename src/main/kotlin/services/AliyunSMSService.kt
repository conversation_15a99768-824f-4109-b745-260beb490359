package com.dzhp.permit.services

import com.aliyun.dysmsapi20170525.Client
import com.aliyun.dysmsapi20170525.models.SendSmsRequest
import com.dzhp.permit.getAliyunSMSClient
import io.ktor.server.application.*
import org.slf4j.LoggerFactory

/**
 * 阿里云短信服务
 * 负责发送短信验证码
 */
class AliyunSMSService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    // 从配置文件获取阿里云短信服务配置
    private val signName: String
        get() = application.environment.config.propertyOrNull("aliyun.sms.signName")?.getString() ?: ""
    private val templateCode: String
        get() = application.environment.config.propertyOrNull("aliyun.sms.templateCode")?.getString() ?: ""

    /**
     * 发送短信验证码
     *
     * @param phoneNumber 手机号码
     * @param code 验证码
     * @return 发送结果
     */
    fun sendVerificationCode(phoneNumber: String, code: String): SMSSendResult {
        try {
            // 获取全局客户端
            val client = try {
                application.getAliyunSMSClient()
            } catch (e: Exception) {
                logger.error("获取阿里云短信客户端失败: ${e.message}")
                return SMSSendResult.Error("短信服务未初始化")
            }

            if (signName.isEmpty() || templateCode.isEmpty()) {
                logger.error("短信签名或模板未配置，无法发送短信")
                return SMSSendResult.Error("短信服务配置不完整")
            }

            // 构建请求
            val sendSmsRequest = SendSmsRequest()
                .setPhoneNumbers(phoneNumber)
                .setSignName(signName)
                .setTemplateCode(templateCode)
                .setTemplateParam("""{"code":"$code"}""")

            // 发送短信
            val response = client.sendSms(sendSmsRequest)
            logger.info(response.toString())

            // 检查响应
            if (response.body.code == "OK") {
                logger.info("短信发送成功: 手机号=$phoneNumber, 验证码=$code, 消息ID=${response.body.bizId}")
                return SMSSendResult.Success(response.body.bizId)
            } else {
                logger.warn("短信发送失败: 手机号=$phoneNumber, 错误码=${response.body.code}, 错误信息=${response.body.message}")
                return SMSSendResult.ApiError(response.body.code, response.body.message)
            }
        } catch (e: Exception) {
            logger.error("发送短信时发生异常: ${e.message}", e)
            return SMSSendResult.Error(e.message ?: "未知错误")
        }
    }
}

/**
 * 短信发送结果
 */
sealed class SMSSendResult {
    /** 发送成功 */
    data class Success(val messageId: String) : SMSSendResult()

    /** API错误 */
    data class ApiError(val code: String, val message: String) : SMSSendResult()

    /** 系统错误 */
    data class Error(val message: String) : SMSSendResult()
}
