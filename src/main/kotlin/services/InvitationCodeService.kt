package com.dzhp.permit.services

import com.dzhp.permit.getMySQLDatabase
import com.dzhp.permit.models.*
import io.ktor.server.application.*
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 邀请码服务
 * 负责邀请码的创建、查询和管理
 */
class InvitationCodeService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val db: Database
        get() = application.getMySQLDatabase()
    val districtService = DistrictService(application)
    private val adminKeyService = AdminKeyService(application, districtService)

    /**
     * 创建新的邀请码
     *
     * @param invitationCode 待创建的邀请码信息
     * @param creator 创建人，如果为null则使用invitationCode中的createBy
     * @return 数据库生成的主键 ID
     */
    fun createInvitationCode(invitationCode: InvitationCode, creator: String? = null): Long =
        transaction(db) {
            InvitationCodes.insert {
                it[code] = invitationCode.code
                it[adminKeyId] = invitationCode.adminKeyId
                it[userType] = invitationCode.userType
                it[permissionLevel] = invitationCode.permissionLevel

                // 新的地域限制字段
                it[province] = invitationCode.province
                it[city] = invitationCode.city
                it[district] = invitationCode.district

                // 兼容旧版本
                it[regionConstraint] = invitationCode.regionConstraint

                // 设置创建人和更新人
                it[createBy] = creator ?: invitationCode.createBy
                it[updateBy] = creator ?: invitationCode.updateBy

                it[status] = invitationCode.status
                it[remainingBalance] = invitationCode.remainingBalance
                it[consumedAmount] = invitationCode.consumedAmount
                it[concurrentLimit] = invitationCode.concurrentLimit
                it[companyName] = invitationCode.companyName

                // 联系方式
                it[contactPerson] = invitationCode.contactPerson
                it[contactPhone] = invitationCode.contactPhone
                it[contactEmail] = invitationCode.contactEmail

                it[createBy] = invitationCode.createBy
                it[updateBy] = invitationCode.updateBy
                it[deleted] = invitationCode.deleted
                it[createdAt] = invitationCode.createdAt
                it[expiresAt] = invitationCode.expiresAt
                it[updateAt] = invitationCode.updateAt
            } get InvitationCodes.id
        }

    /**
     * 获取所有邀请码列表
     *
     * @param onlyActive 是否只返回有效的邀请码，默认为 true
     * @return 邀请码列表
     */
    fun listInvitationCodes(onlyActive: Boolean = true): List<InvitationCode> =
        transaction(db) {
            val query = if (onlyActive) {
                InvitationCodes.selectAll().where { (InvitationCodes.status eq InvitationCodeStatus.ACTIVE) and (InvitationCodes.deleted eq false) }
            } else {
                InvitationCodes.selectAll().where { InvitationCodes.deleted eq false }
            }

            query.map { row ->
                InvitationCode(
                    id = row[InvitationCodes.id],
                    code = row[InvitationCodes.code],
                    adminKeyId = row[InvitationCodes.adminKeyId],
                    userType = row[InvitationCodes.userType],
                    permissionLevel = row[InvitationCodes.permissionLevel],
                    province = row[InvitationCodes.province],
                    city = row[InvitationCodes.city],
                    district = row[InvitationCodes.district],
                    regionConstraint = row[InvitationCodes.regionConstraint],
                    status = row[InvitationCodes.status],
                    remainingBalance = row[InvitationCodes.remainingBalance],
                    consumedAmount = row[InvitationCodes.consumedAmount],
                    concurrentLimit = row[InvitationCodes.concurrentLimit],
                    companyName = row[InvitationCodes.companyName],
                    contactPerson = row[InvitationCodes.contactPerson],
                    contactPhone = row[InvitationCodes.contactPhone],
                    contactEmail = row[InvitationCodes.contactEmail],
                    createBy = row[InvitationCodes.createBy],
                    updateBy = row[InvitationCodes.updateBy],
                    deleted = row[InvitationCodes.deleted],
                    createdAt = row[InvitationCodes.createdAt],
                    expiresAt = row[InvitationCodes.expiresAt],
                    updateAt = row[InvitationCodes.updateAt]
                )
            }
        }

    /**
     * 根据邀请码字符串查询邀请码
     *
     * @param code 邀请码字符串
     * @return 邀请码对象，如果不存在则返回 null
     */
    fun findInvitationCodeByCode(code: String): InvitationCode? =
        transaction(db) {
            InvitationCodes.selectAll().where {
                (InvitationCodes.code eq code) and (InvitationCodes.deleted eq false)
            }.map { row ->
                InvitationCode(
                    id = row[InvitationCodes.id],
                    code = row[InvitationCodes.code],
                    adminKeyId = row[InvitationCodes.adminKeyId],
                    userType = row[InvitationCodes.userType],
                    permissionLevel = row[InvitationCodes.permissionLevel],
                    province = row[InvitationCodes.province],
                    city = row[InvitationCodes.city],
                    district = row[InvitationCodes.district],
                    regionConstraint = row[InvitationCodes.regionConstraint],
                    status = row[InvitationCodes.status],
                    remainingBalance = row[InvitationCodes.remainingBalance],
                    consumedAmount = row[InvitationCodes.consumedAmount],
                    concurrentLimit = row[InvitationCodes.concurrentLimit],
                    companyName = row[InvitationCodes.companyName],
                    contactPerson = row[InvitationCodes.contactPerson],
                    contactPhone = row[InvitationCodes.contactPhone],
                    contactEmail = row[InvitationCodes.contactEmail],
                    createBy = row[InvitationCodes.createBy],
                    updateBy = row[InvitationCodes.updateBy],
                    deleted = row[InvitationCodes.deleted],
                    createdAt = row[InvitationCodes.createdAt],
                    expiresAt = row[InvitationCodes.expiresAt],
                    updateAt = row[InvitationCodes.updateAt]
                )
            }.singleOrNull()
        }

    /**
     * 验证邀请码是否有效
     *
     * @param code 邀请码字符串
     * @return 验证结果，包含成功/失败状态和邀请码对象（如果成功）
     */
    fun validateInvitationCode(code: String): InvitationCodeValidationResult {
        val invitationCode = findInvitationCodeByCode(code)
            ?: return InvitationCodeValidationResult.NotFound

        // 检查邀请码状态
        if (invitationCode.status != InvitationCodeStatus.ACTIVE) {
            return InvitationCodeValidationResult.Inactive
        }

        // 检查是否过期
        if (invitationCode.expiresAt < System.currentTimeMillis()) {
            return InvitationCodeValidationResult.Expired
        }

        // 检查余额
        if (invitationCode.remainingBalance <= 0) {
            return InvitationCodeValidationResult.InsufficientBalance
        }

        // 所有检查通过，邀请码有效
        return InvitationCodeValidationResult.Valid(invitationCode)
    }

    /**
     * 根据公司ID查询邀请码列表
     *
     * @param companyName 公司ID
     * @param onlyActive 是否只返回有效的邀请码，默认为 true
     * @return 邀请码列表
     */
    fun findInvitationCodesByCompanyName(companyName: String, onlyActive: Boolean = true): List<InvitationCode> =
        transaction(db) {
            val query = if (onlyActive) {
                InvitationCodes.selectAll().where {
                    (InvitationCodes.companyName eq companyName) and
                    (InvitationCodes.status eq InvitationCodeStatus.ACTIVE) and
                    (InvitationCodes.deleted eq false)
                }
            } else {
                InvitationCodes.selectAll().where {
                    (InvitationCodes.companyName eq companyName) and
                    (InvitationCodes.deleted eq false)
                }
            }

            query.map { row ->
                InvitationCode(
                    id = row[InvitationCodes.id],
                    code = row[InvitationCodes.code],
                    adminKeyId = row[InvitationCodes.adminKeyId],
                    userType = row[InvitationCodes.userType],
                    permissionLevel = row[InvitationCodes.permissionLevel],
                    province = row[InvitationCodes.province],
                    city = row[InvitationCodes.city],
                    district = row[InvitationCodes.district],
                    regionConstraint = row[InvitationCodes.regionConstraint],
                    status = row[InvitationCodes.status],
                    remainingBalance = row[InvitationCodes.remainingBalance],
                    consumedAmount = row[InvitationCodes.consumedAmount],
                    concurrentLimit = row[InvitationCodes.concurrentLimit],
                    companyName = row[InvitationCodes.companyName],
                    contactPerson = row[InvitationCodes.contactPerson],
                    contactPhone = row[InvitationCodes.contactPhone],
                    contactEmail = row[InvitationCodes.contactEmail],
                    createBy = row[InvitationCodes.createBy],
                    updateBy = row[InvitationCodes.updateBy],
                    deleted = row[InvitationCodes.deleted],
                    createdAt = row[InvitationCodes.createdAt],
                    expiresAt = row[InvitationCodes.expiresAt],
                    updateAt = row[InvitationCodes.updateAt]
                )
            }
        }

    /**
     * 生成随机邀请码
     * 二级邀请码格式: 地区邮编（6位）+ 随机数字（8位）
     * 例如：20000012345678
     *
     * @param adminKeyId 管理员密钥ID，用于获取一级邀请码
     * @param province 省份名称
     * @param city 城市名称
     * @param district 区县名称
     * @return 生成的随机邀请码字符串
     */
    fun generateRandomCode(adminKeyId: Long? = null, province: String? = null, city: String? = null, district: String? = null): String {
        // 如果提供了管理员密钥ID，则生成二级邀请码
        if (adminKeyId != null) {
            val adminKey = adminKeyService.findAdminKeyById(adminKeyId)
            if (adminKey != null) {
                // 使用管理员密钥的地区信息或用户提供的地区信息
                val useProvince = province ?: adminKey.province.takeIf { it!!.isNotBlank() } ?: "上海市"
                val useCity = city ?: adminKey.city.takeIf { it!!.isNotBlank() } ?: "上海市"
                val useDistrict = district ?: adminKey.district.takeIf { it!!.isNotBlank() } ?: "黄浦区"

                return adminKeyService.generateSecondaryInvitationCode(useProvince, useCity, useDistrict)
            }
        }

        // 如果没有提供管理员密钥ID或找不到管理员密钥，则生成一个随机的UUID作为邀请码
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16).uppercase()
    }

    /**
     * 获取所有邀请码列表
     *
     * @param onlyActive 是否只返回有效的邀请码，默认为 true
     * @return 邀请码列表
     */
    fun listAllInvitationCodes(onlyActive: Boolean = true): List<InvitationCode> =
        transaction(db) {
            val query = if (onlyActive) {
                InvitationCodes.selectAll().where {
                    (InvitationCodes.status eq InvitationCodeStatus.ACTIVE) and
                    (InvitationCodes.deleted eq false)
                }
            } else {
                InvitationCodes.selectAll().where {
                    (InvitationCodes.deleted eq false)
                }
            }

            query.map { row ->
                InvitationCode(
                    id = row[InvitationCodes.id],
                    code = row[InvitationCodes.code],
                    adminKeyId = row[InvitationCodes.adminKeyId],
                    userType = row[InvitationCodes.userType],
                    permissionLevel = row[InvitationCodes.permissionLevel],
                    province = row[InvitationCodes.province],
                    city = row[InvitationCodes.city],
                    district = row[InvitationCodes.district],
                    regionConstraint = row[InvitationCodes.regionConstraint],
                    status = row[InvitationCodes.status],
                    remainingBalance = row[InvitationCodes.remainingBalance],
                    consumedAmount = row[InvitationCodes.consumedAmount],
                    concurrentLimit = row[InvitationCodes.concurrentLimit],
                    companyName = row[InvitationCodes.companyName],
                    contactPerson = row[InvitationCodes.contactPerson],
                    contactPhone = row[InvitationCodes.contactPhone],
                    contactEmail = row[InvitationCodes.contactEmail],
                    createBy = row[InvitationCodes.createBy],
                    updateBy = row[InvitationCodes.updateBy],
                    deleted = row[InvitationCodes.deleted],
                    createdAt = row[InvitationCodes.createdAt],
                    expiresAt = row[InvitationCodes.expiresAt],
                    updateAt = row[InvitationCodes.updateAt]
                )
            }
        }

    /**
     * 根据管理员密钥ID查询邀请码列表
     *
     * @param adminKeyId 管理员密钥ID
     * @param onlyActive 是否只返回有效的邀请码，默认为 true
     * @return 邀请码列表
     */
    fun findInvitationCodesByAdminKeyId(adminKeyId: Long, onlyActive: Boolean = true): List<InvitationCode> =
        transaction(db) {
            val query = if (onlyActive) {
                InvitationCodes.selectAll().where {
                    (InvitationCodes.adminKeyId eq adminKeyId) and
                    (InvitationCodes.status eq InvitationCodeStatus.ACTIVE) and
                    (InvitationCodes.deleted eq false)
                }
            } else {
                InvitationCodes.selectAll().where {
                    (InvitationCodes.adminKeyId eq adminKeyId) and
                    (InvitationCodes.deleted eq false)
                }
            }

            query.map { row ->
                InvitationCode(
                    id = row[InvitationCodes.id],
                    code = row[InvitationCodes.code],
                    adminKeyId = row[InvitationCodes.adminKeyId],
                    userType = row[InvitationCodes.userType],
                    permissionLevel = row[InvitationCodes.permissionLevel],
                    province = row[InvitationCodes.province],
                    city = row[InvitationCodes.city],
                    district = row[InvitationCodes.district],
                    regionConstraint = row[InvitationCodes.regionConstraint],
                    status = row[InvitationCodes.status],
                    remainingBalance = row[InvitationCodes.remainingBalance],
                    consumedAmount = row[InvitationCodes.consumedAmount],
                    concurrentLimit = row[InvitationCodes.concurrentLimit],
                    companyName = row[InvitationCodes.companyName],
                    contactPerson = row[InvitationCodes.contactPerson],
                    contactPhone = row[InvitationCodes.contactPhone],
                    contactEmail = row[InvitationCodes.contactEmail],
                    createBy = row[InvitationCodes.createBy],
                    updateBy = row[InvitationCodes.updateBy],
                    deleted = row[InvitationCodes.deleted],
                    createdAt = row[InvitationCodes.createdAt],
                    expiresAt = row[InvitationCodes.expiresAt],
                    updateAt = row[InvitationCodes.updateAt]
                )
            }
        }

    /**
     * 根据ID查询邀请码
     *
     * @param id 邀请码ID
     * @return 邀请码对象，如果不存在则返回 null
     */
    fun findInvitationCodeById(id: Long): InvitationCode? =
        transaction(db) {
            InvitationCodes.selectAll().where {
                (InvitationCodes.id eq id) and (InvitationCodes.deleted eq false)
            }.map { row ->
                InvitationCode(
                    id = row[InvitationCodes.id],
                    code = row[InvitationCodes.code],
                    adminKeyId = row[InvitationCodes.adminKeyId],
                    userType = row[InvitationCodes.userType],
                    permissionLevel = row[InvitationCodes.permissionLevel],
                    province = row[InvitationCodes.province],
                    city = row[InvitationCodes.city],
                    district = row[InvitationCodes.district],
                    regionConstraint = row[InvitationCodes.regionConstraint],
                    status = row[InvitationCodes.status],
                    remainingBalance = row[InvitationCodes.remainingBalance],
                    consumedAmount = row[InvitationCodes.consumedAmount],
                    concurrentLimit = row[InvitationCodes.concurrentLimit],
                    companyName = row[InvitationCodes.companyName],
                    contactPerson = row[InvitationCodes.contactPerson],
                    contactPhone = row[InvitationCodes.contactPhone],
                    contactEmail = row[InvitationCodes.contactEmail],
                    createBy = row[InvitationCodes.createBy],
                    updateBy = row[InvitationCodes.updateBy],
                    deleted = row[InvitationCodes.deleted],
                    createdAt = row[InvitationCodes.createdAt],
                    expiresAt = row[InvitationCodes.expiresAt],
                    updateAt = row[InvitationCodes.updateAt]
                )
            }.singleOrNull()
        }

    /**
     * 更新邀请码信息
     *
     * @param id 邀请码ID
     * @param updates 要更新的邀请码信息
     * @param updateBy 更新人
     * @return 是否更新成功
     */
    fun updateInvitationCode(id: Long, updates: InvitationCode, updateBy: String? = null): Boolean =
        transaction(db) {
            val updatedRows = InvitationCodes.update({ (InvitationCodes.id eq id) and (InvitationCodes.deleted eq false) }) {
                // 不允许修改的字段：id, code, adminKeyId, createdAt
                it[userType] = updates.userType
                it[permissionLevel] = updates.permissionLevel
                it[province] = updates.province
                it[city] = updates.city
                it[district] = updates.district
                it[regionConstraint] = updates.regionConstraint
                it[status] = updates.status
                it[remainingBalance] = updates.remainingBalance
                it[consumedAmount] = updates.consumedAmount
                it[concurrentLimit] = updates.concurrentLimit
                it[companyName] = updates.companyName

                // 联系方式
                it[contactPerson] = updates.contactPerson
                it[contactPhone] = updates.contactPhone
                it[contactEmail] = updates.contactEmail

                it[InvitationCodes.updateBy] = updateBy ?: updates.updateBy
                it[expiresAt] = updates.expiresAt
                it[updateAt] = System.currentTimeMillis()
            }
            updatedRows > 0
        }

    /**
     * 软删除邀请码
     *
     * @param id 邀请码ID
     * @param updateBy 更新人
     * @return 是否删除成功
     */
    fun deleteInvitationCode(id: Long, updateBy: String? = null): Boolean =
        transaction(db) {
            val updatedRows = InvitationCodes.update({ (InvitationCodes.id eq id) and (InvitationCodes.deleted eq false) }) {
                it[deleted] = true
                it[InvitationCodes.updateBy] = updateBy
                it[updateAt] = System.currentTimeMillis()
            }
            updatedRows > 0
        }

    /**
     * 检查邀请码是否属于指定的管理员密钥
     *
     * @param invitationCodeId 邀请码ID
     * @param adminKeyId 管理员密钥ID
     * @return 如果邀请码属于指定的管理员密钥则返回true，否则返回false
     */
    fun isInvitationCodeOwnedByAdminKey(invitationCodeId: Long, adminKeyId: Long): Boolean =
        transaction(db) {
            val count = InvitationCodes.selectAll().where {
                (InvitationCodes.id eq invitationCodeId) and
                (InvitationCodes.adminKeyId eq adminKeyId) and
                (InvitationCodes.deleted eq false)
            }.count()
            count > 0
        }
}

/**
 * 邀请码验证结果
 */
sealed class InvitationCodeValidationResult {
    /** 邀请码有效 */
    data class Valid(val invitationCode: InvitationCode) : InvitationCodeValidationResult()

    /** 邀请码不存在 */
    object NotFound : InvitationCodeValidationResult()

    /** 邀请码已停用 */
    object Inactive : InvitationCodeValidationResult()

    /** 邀请码已过期 */
    object Expired : InvitationCodeValidationResult()

    /** 邀请码余额不足 */
    object InsufficientBalance : InvitationCodeValidationResult()
}
