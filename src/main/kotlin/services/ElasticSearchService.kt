package com.dzhp.permit.services

import com.google.gson.Gson
import io.ktor.server.application.*
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest
import org.elasticsearch.action.bulk.BulkRequest
import org.elasticsearch.action.bulk.BulkResponse
import org.elasticsearch.action.index.IndexRequest
import org.elasticsearch.action.index.IndexResponse
import org.elasticsearch.action.search.SearchRequest
import org.elasticsearch.action.search.SearchResponse
import org.elasticsearch.client.RequestOptions
import org.elasticsearch.client.RestHighLevelClient
import org.elasticsearch.client.indices.CreateIndexRequest
import org.elasticsearch.client.indices.GetIndexRequest
import org.elasticsearch.common.xcontent.XContentType
import org.elasticsearch.index.query.QueryBuilder
import org.elasticsearch.search.builder.SearchSourceBuilder
import org.slf4j.LoggerFactory
import com.dzhp.permit.getElasticSearchClient
import java.util.*

/**
 * ElasticSearch服务
 * 提供与ElasticSearch交互的高级方法
 */
class ElasticSearchService(
    private val application: Application
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val gson = Gson()
    private val client: RestHighLevelClient
        get() = application.getElasticSearchClient()

    /**
     * 检查索引是否存在
     *
     * @param indexName 索引名称
     * @return 索引是否存在
     */
    fun indexExists(indexName: String): Boolean {
        val request = GetIndexRequest(indexName)
        return client.indices().exists(request, RequestOptions.DEFAULT)
    }

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @param mappings 索引映射（JSON字符串）
     * @param settings 索引设置（JSON字符串）
     * @return 是否创建成功
     */
    fun createIndex(indexName: String, mappings: String? = null, settings: String? = null): Boolean {
        if (indexExists(indexName)) {
            logger.info("Index $indexName already exists")
            return true
        }

        val request = CreateIndexRequest(indexName)
        
        if (!mappings.isNullOrEmpty()) {
            request.mapping(mappings, XContentType.JSON)
        }
        
        if (!settings.isNullOrEmpty()) {
            request.settings(settings, XContentType.JSON)
        }
        
        val response = client.indices().create(request, RequestOptions.DEFAULT)
        return response.isAcknowledged
    }

    /**
     * 删除索引
     *
     * @param indexName 索引名称
     * @return 是否删除成功
     */
    fun deleteIndex(indexName: String): Boolean {
        if (!indexExists(indexName)) {
            logger.info("Index $indexName does not exist")
            return true
        }
        
        val request = DeleteIndexRequest(indexName)
        val response = client.indices().delete(request, RequestOptions.DEFAULT)
        return response.isAcknowledged
    }

    /**
     * 索引单个文档
     *
     * @param indexName 索引名称
     * @param document 文档对象
     * @param id 文档ID（可选，如果不提供则自动生成）
     * @return 索引响应
     */
    fun <T> indexDocument(indexName: String, document: T, id: String = UUID.randomUUID().toString()): IndexResponse {
        val jsonString = gson.toJson(document)
        val request = IndexRequest(indexName)
            .id(id)
            .source(jsonString, XContentType.JSON)
        
        return client.index(request, RequestOptions.DEFAULT)
    }

    /**
     * 批量索引文档
     *
     * @param indexName 索引名称
     * @param documents 文档对象列表
     * @param idExtractor 从文档中提取ID的函数（可选，如果不提供则自动生成ID）
     * @return 批量索引响应
     */
    fun <T> bulkIndexDocuments(
        indexName: String,
        documents: List<T>,
        idExtractor: ((T) -> String)? = null
    ): BulkResponse {
        val bulkRequest = BulkRequest()
        
        documents.forEach { document ->
            val id = idExtractor?.invoke(document) ?: UUID.randomUUID().toString()
            val jsonString = gson.toJson(document)
            
            val indexRequest = IndexRequest(indexName)
                .id(id)
                .source(jsonString, XContentType.JSON)
            
            bulkRequest.add(indexRequest)
        }
        
        return client.bulk(bulkRequest, RequestOptions.DEFAULT)
    }

    /**
     * 搜索文档
     *
     * @param indexName 索引名称
     * @param queryBuilder 查询构建器
     * @param from 起始位置
     * @param size 返回数量
     * @return 搜索响应
     */
    fun search(
        indexName: String,
        queryBuilder: QueryBuilder,
        from: Int = 0,
        size: Int = 10
    ): SearchResponse {
        val searchSourceBuilder = SearchSourceBuilder()
            .query(queryBuilder)
            .from(from)
            .size(size)
        
        val searchRequest = SearchRequest(indexName)
            .source(searchSourceBuilder)
        
        return client.search(searchRequest, RequestOptions.DEFAULT)
    }

    /**
     * 解析搜索结果为指定类型
     *
     * @param response 搜索响应
     * @param clazz 目标类型
     * @return 解析后的对象列表
     */
    fun <T> parseSearchResponse(response: SearchResponse, clazz: Class<T>): List<T> {
        return response.hits.hits.mapNotNull { hit ->
            try {
                gson.fromJson(hit.sourceAsString, clazz)
            } catch (e: Exception) {
                logger.error("Error parsing search result: ${e.message}", e)
                null
            }
        }
    }
}
