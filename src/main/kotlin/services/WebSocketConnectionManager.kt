package com.dzhp.permit.services

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.exceptions.JWTVerificationException
import io.ktor.server.application.*
import io.ktor.server.websocket.*
import io.ktor.websocket.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap

/**
 * WebSocket连接管理器
 * 负责管理WebSocket连接和并发控制
 */
class WebSocketConnectionManager(private val application: Application) {
    private val logger = LoggerFactory.getLogger(javaClass)

    // JWT配置
    private val jwtSecret: String
        get() = application.environment.config.property("jwt.secret").getString()
    private val jwtIssuer: String
        get() = application.environment.config.property("jwt.domain").getString()
    private val jwtAudience: String
        get() = application.environment.config.property("jwt.audience").getString()

    // 房间锁，用于同步对房间的操作
    private val roomsMutex = Mutex()

    // 房间映射，key是邀请码，value是该房间中的所有连接
    private val rooms = ConcurrentHashMap<String, MutableSet<Connection>>()

    /**
     * WebSocket连接信息
     */
    data class Connection(
        val session: DefaultWebSocketSession,
        val token: String,
        val invitationCode: String
    )

    /**
     * 检查邀请码的并发连接数是否已达到限制
     *
     * @param roomId 房间ID（邀请码）
     * @param limit 并发限制数
     * @return 如果未达到并发限制则返回true，否则返回false
     */
    suspend fun checkConcurrencyLimit(roomId: String, limit: Int): Boolean {
        return roomsMutex.withLock {
            val room = rooms[roomId]
            val count = room?.size ?: 0
            count < limit
        }
    }

    /**
     * 添加新的WebSocket连接
     *
     * @param session WebSocket会话
     * @param token JWT令牌
     * @return 是否成功添加连接
     */
    suspend fun addConnection(session: DefaultWebSocketSession, token: String): Boolean {
        try {
            logger.info("JWT验证配置: audience=$jwtAudience, issuer=$jwtIssuer")
            // 验证JWT令牌
            val verifier = JWT.require(Algorithm.HMAC256(jwtSecret))
                .withAudience(jwtAudience)
                .withIssuer(jwtIssuer)
                .build()

            // 从JWT中获取邀请码
            val jwt = verifier.verify(token)
            val invitationCode = jwt.getClaim("code").asString()
            if (invitationCode.isNullOrEmpty()) {
                logger.warn("Invalid token: invitation code claim is missing")
                return false
            }

            // 输出所有的claims以便于调试
            logger.info("JWT验证成功, 邀请码: $invitationCode, 所有claims: ${jwt.claims.map { "${it.key}=${it.value.asString()}" }}")

            // 创建连接对象
            val connection = Connection(session, token, invitationCode)

            // 添加到对应的房间
            roomsMutex.withLock {
                val room = rooms.getOrPut(invitationCode) { mutableSetOf() }
                room.add(connection)
                logger.info("Added connection to room $invitationCode, current connections: ${room.size}")
            }

            return true
        } catch (e: JWTVerificationException) {
            logger.warn("JWT verification failed: ${e.message}")
            return false
        } catch (e: Exception) {
            logger.error("Error adding connection: ${e.message}", e)
            return false
        }
    }

    /**
     * 移除WebSocket连接
     *
     * @param session WebSocket会话
     */
    suspend fun removeConnection(session: DefaultWebSocketSession) {
        roomsMutex.withLock {
            // 查找并移除连接
            for ((roomId, connections) in rooms) {
                val connection = connections.find { it.session === session }
                if (connection != null) {
                    connections.remove(connection)
                    logger.info("Removed connection from room $roomId, remaining connections: ${connections.size}")

                    // 如果房间为空，移除房间
                    if (connections.isEmpty()) {
                        rooms.remove(roomId)
                        logger.info("Removed empty room: $roomId")
                    }

                    break
                }
            }
        }
    }

    /**
     * 根据令牌移除连接
     *
     * @param token JWT令牌
     * @return 是否成功移除连接
     */
    suspend fun removeConnectionByToken(token: String): Boolean {
        try {
            // 验证JWT令牌
            val verifier = JWT.require(Algorithm.HMAC256(jwtSecret))
                .withAudience(jwtAudience)
                .withIssuer(jwtIssuer)
                .build()

            val jwt = verifier.verify(token)

            // 从JWT中获取邀请码
            val invitationCode = jwt.getClaim("code").asString()
            if (invitationCode.isNullOrEmpty()) {
                logger.warn("Invalid token: invitation code claim is missing")
                return false
            }

            var removed = false

            roomsMutex.withLock {
                val room = rooms[invitationCode]
                if (room != null) {
                    // 查找并移除匹配令牌的连接
                    val iterator = room.iterator()
                    while (iterator.hasNext()) {
                        val connection = iterator.next()
                        if (connection.token == token) {
                            // 关闭WebSocket连接
                            try {
                                connection.session.close(CloseReason(CloseReason.Codes.NORMAL, "User logged out"))
                            } catch (e: Exception) {
                                logger.warn("Error closing WebSocket connection: ${e.message}")
                            }

                            // 从集合中移除
                            iterator.remove()
                            removed = true
                            logger.info("Removed connection with token from room $invitationCode, remaining connections: ${room.size}")
                        }
                    }

                    // 如果房间为空，移除房间
                    if (room.isEmpty()) {
                        rooms.remove(invitationCode)
                        logger.info("Removed empty room: $invitationCode")
                    }
                }
            }

            return removed
        } catch (e: JWTVerificationException) {
            logger.warn("JWT verification failed: ${e.message}")
            return false
        } catch (e: Exception) {
            logger.error("Error removing connection by token: ${e.message}", e)
            return false
        }
    }

    /**
     * 获取房间中的连接数
     *
     * @param roomId 房间ID（邀请码）
     * @return 连接数
     */
    suspend fun getConnectionCount(roomId: String): Int {
        return roomsMutex.withLock {
            rooms[roomId]?.size ?: 0
        }
    }

    /**
     * 获取所有房间的连接统计
     *
     * @return 房间ID到连接数的映射
     */
    suspend fun getAllRoomStats(): Map<String, Int> {
        return roomsMutex.withLock {
            rooms.mapValues { it.value.size }
        }
    }

    /**
     * 踢出指定邀请码的所有连接
     * 关闭WebSocket连接并清空JWT
     *
     * @param invitationCode 邀请码
     * @return 踢出的连接数
     */
    suspend fun kickConnectionsByInvitationCode(invitationCode: String): Int {
        return roomsMutex.withLock {
            val room = rooms[invitationCode]
            if (room == null) {
                return@withLock 0
            }

            val connectionCount = room.size

            // 关闭所有连接
            room.forEach { connection ->
                try {
                    connection.session.close(CloseReason(CloseReason.Codes.NORMAL, "Kicked by administrator"))
                } catch (e: Exception) {
                    logger.warn("Error closing WebSocket connection: ${e.message}")
                }
            }

            // 清空房间
            rooms.remove(invitationCode)
            logger.info("Kicked all connections from room $invitationCode, total connections: $connectionCount")

            connectionCount
        }
    }
}
