package com.dzhp.permit

import io.ktor.server.application.*
import org.apache.http.HttpHost
import org.apache.http.auth.AuthScope
import org.apache.http.auth.UsernamePasswordCredentials
import org.apache.http.client.CredentialsProvider
import org.apache.http.impl.client.BasicCredentialsProvider
import org.elasticsearch.client.RequestOptions
import org.elasticsearch.client.RestClient
import org.elasticsearch.client.RestHighLevelClient
import org.slf4j.LoggerFactory

// 全局ElasticSearch客户端
private lateinit var elasticSearchClient: RestHighLevelClient

/**
 * 配置ElasticSearch连接
 * 从配置文件读取连接参数并初始化客户端
 */
fun Application.configureElasticSearch() {
    val logger = LoggerFactory.getLogger("ElasticSearch")

    try {
        // 从配置文件获取ElasticSearch连接参数
        val host = environment.config.property("elasticsearch.host").getString()
        val port = environment.config.property("elasticsearch.port").getString().toInt()
        val scheme = environment.config.propertyOrNull("elasticsearch.scheme")?.getString() ?: "http"
        val username = environment.config.propertyOrNull("elasticsearch.username")?.getString()
        val password = environment.config.propertyOrNull("elasticsearch.password")?.getString()

        // 创建HTTP主机配置
        val httpHost = HttpHost(host, port, scheme)

        // 构建客户端
        val builder = RestClient.builder(httpHost)

        // 如果提供了用户名和密码，配置认证
        if (!username.isNullOrEmpty() && !password.isNullOrEmpty()) {
            val credentialsProvider: CredentialsProvider = BasicCredentialsProvider()
            credentialsProvider.setCredentials(
                AuthScope(host, port), UsernamePasswordCredentials(username, password)
            )

            builder.setHttpClientConfigCallback { httpClientBuilder ->
                httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
            }
        }

        // 创建高级客户端
        elasticSearchClient = RestHighLevelClient(builder)

        // 测试连接
        try {
            val pingResponse = elasticSearchClient.ping(RequestOptions.DEFAULT)
            if (pingResponse) {
                logger.info("Successfully connected to ElasticSearch at $host:$port")
            } else {
                logger.error("Failed to connect to ElasticSearch at $host:$port")
            }
        } catch (e: Exception) {
            logger.error("Failed to ping ElasticSearch at $host:$port: ${e.message}")
            throw e
        }
    } catch (e: Exception) {
        logger.error("Error configuring ElasticSearch: ${e.message}", e)
        throw e
    }
}

/**
 * 获取ElasticSearch客户端的扩展函数
 * @return ElasticSearch高级客户端实例
 */
fun Application.getElasticSearchClient(): RestHighLevelClient = elasticSearchClient

/**
 * 关闭ElasticSearch客户端连接
 */
fun Application.closeElasticSearch() {
    if (::elasticSearchClient.isInitialized) {
        try {
            elasticSearchClient.close()
        } catch (e: Exception) {
            LoggerFactory.getLogger("ElasticSearch").error("Error closing ElasticSearch client: ${e.message}", e)
        }
    }
}