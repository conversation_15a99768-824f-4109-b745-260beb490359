package com.dzhp.permit

import com.aliyun.dysmsapi20170525.Client
import com.aliyun.teaopenapi.models.Config
import io.ktor.server.application.*
import org.slf4j.LoggerFactory

// 全局阿里云短信客户端
private lateinit var aliyunSMSClient: Client

/**
 * 配置阿里云短信服务
 * 从配置文件读取连接参数并初始化短信客户端
 */
fun Application.configureAliyunSMS() {
    val logger = LoggerFactory.getLogger("AliyunSMS")

    try {
        // 从配置文件获取阿里云短信服务配置
        val accessKeyId = environment.config.propertyOrNull("aliyun.sms.accessKeyId")?.getString() ?: ""
        val accessKeySecret = environment.config.propertyOrNull("aliyun.sms.accessKeySecret")?.getString() ?: ""
        val endpoint = environment.config.propertyOrNull("aliyun.sms.endpoint")?.getString() ?: "dysmsapi.aliyuncs.com"

        if (accessKeyId.isEmpty() || accessKeySecret.isEmpty()) {
            logger.warn("阿里云短信服务配置缺失，无法初始化客户端")
            return
        }

        // 创建阿里云短信客户端配置
        val config = Config()
            .setAccessKeyId(accessKeyId)
            .setAccessKeySecret(accessKeySecret)
            .setEndpoint(endpoint)

        // 初始化客户端
        aliyunSMSClient = Client(config)
        logger.info("阿里云短信客户端初始化成功")

    } catch (e: Exception) {
        logger.error("初始化阿里云短信客户端失败: ${e.message}", e)
    }
}

/**
 * 获取阿里云短信客户端
 *
 * @return 阿里云短信客户端
 */
fun Application.getAliyunSMSClient(): Client {
    return aliyunSMSClient
}

/**
 * 关闭阿里云短信客户端
 * 目前阿里云短信客户端不需要显式关闭，此方法为保持一致性而添加
 */
fun Application.closeAliyunSMS() {
    // 阿里云短信客户端不需要显式关闭
}
