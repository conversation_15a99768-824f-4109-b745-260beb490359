package com.dzhp.permit

import io.ktor.server.application.*
import redis.clients.jedis.Jedis
import redis.clients.jedis.JedisPool
import redis.clients.jedis.JedisPoolConfig

// 全局Redis连接池
private lateinit var jedisPool: JedisPool

fun Application.configureRedis() {
    // 从配置文件获取Redis连接参数
    val host = environment.config.property("redis.host").getString()
    val port = environment.config.property("redis.port").getString().toInt()
    val password = environment.config.propertyOrNull("redis.password")?.getString()
    val database = environment.config.propertyOrNull("redis.database")?.getString()?.toInt() ?: 0
    val timeout = environment.config.propertyOrNull("redis.timeout")?.getString()?.toInt() ?: 2000
    
    // 配置连接池
    val poolConfig = JedisPoolConfig().apply {
        maxTotal = environment.config.propertyOrNull("redis.pool.maxTotal")?.getString()?.toInt() ?: 16
        maxIdle = environment.config.propertyOrNull("redis.pool.maxIdle")?.getString()?.toInt() ?: 8
        minIdle = environment.config.propertyOrNull("redis.pool.minIdle")?.getString()?.toInt() ?: 4
        testOnBorrow = true
        testOnReturn = true
    }
    
    // 创建连接池
    jedisPool = if (password.isNullOrEmpty()) {
        JedisPool(poolConfig, host, port, timeout, null, database)
    } else {
        JedisPool(poolConfig, host, port, timeout, password, database)
    }
    
    // 测试连接
    jedisPool.resource.use { jedis ->
        jedis.ping()
    }
}

// 提供获取Jedis连接的扩展函数
fun Application.getRedisConnection(): Jedis = jedisPool.resource

