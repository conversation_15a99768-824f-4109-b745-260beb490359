package com.dzhp.permit.routers.llm

import com.dzhp.permit.models.EmptyResponseData
import com.dzhp.permit.models.PermitAgentListResponse
import com.dzhp.permit.models.PermitAgentTagsResponse
import com.dzhp.permit.models.standardResponse
import com.dzhp.permit.services.PermitAgentService
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*

/**
 * 代理系统路由
 * 提供代理系统相关的API接口
 *
 * - GET /api/agent/list : 获取代理列表
 * - GET /api/agent/tags : 获取所有代理标签
 */
fun Application.permitAgentRouter(permitAgentService: PermitAgentService) {
    routing {
        route("/api/agent") {
            /**
             * 获取代理列表
             * 返回所有在线的代理
             */
            get("/list") {
                try {
                    val agents = permitAgentService.searchAgentList()

                    val responseData = PermitAgentListResponse(
                        agents = agents,
                        total = agents.size
                    )

                    call.respond(
                        HttpStatusCode.OK,
                        standardResponse(
                            code = HttpStatusCode.OK.value,
                            message = "获取代理列表成功",
                            data = responseData
                        )
                    )
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.InternalServerError.value,
                            message = "获取代理列表失败: ${e.message}"
                        )
                    )
                }
            }

            /**
             * 获取所有代理标签
             * 返回所有在线代理的标签列表（去重）
             */
            get("/tags") {
                try {
                    val tags = permitAgentService.searchAgentTagList()

                    val responseData = PermitAgentTagsResponse(
                        tags = tags
                    )

                    call.respond(
                        HttpStatusCode.OK,
                        standardResponse(
                            code = HttpStatusCode.OK.value,
                            message = "获取代理标签列表成功",
                            data = responseData
                        )
                    )
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.InternalServerError.value,
                            message = "获取代理标签列表失败: ${e.message}"
                        )
                    )
                }
            }
        }
    }
}
