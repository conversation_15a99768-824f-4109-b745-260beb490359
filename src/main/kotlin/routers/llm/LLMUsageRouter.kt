package com.dzhp.permit.routers.llm

import com.dzhp.permit.models.LLMUsage
import com.dzhp.permit.service.LLMUsageService
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import java.util.UUID

/**
 * 只负责把 LLM 使用流水写入数据库的路由
 *
 * - POST /api/llm/usage         : 单条写入，返回主键 id
 * - POST /api/llm/usage/batch   : 批量写入，返回成功行数
 */
fun Application.llmUsageRouter(usageService: LLMUsageService) {

    routing {
        route("/api/llm/usage") {

            /** 单条写入 */
            post {
                // 接收原始使用记录
                val usage = call.receive<LLMUsage>()

                // 尝试从JWT令牌中提取用户ID
                val userId = call.principal<JWTPrincipal>()?.getClaim("userId", String::class)
                    ?: call.principal<JWTPrincipal>()?.getClaim("code", String::class)

                // 生成请求ID（如果未提供）
                val requestId = usage.requestId ?: UUID.randomUUID().toString()

                // 创建包含用户ID和请求ID的新使用记录
                val enrichedUsage = usage.copy(
                    userId = usage.userId ?: userId,
                    requestId = requestId
                )

                // 记录使用情况
                val id = usageService.recordUsage(enrichedUsage)
                call.respond(HttpStatusCode.Created, mapOf("id" to id))
            }

            /** 批量写入 */
            post("/batch") {
                // 接收原始使用记录列表
                val usages = call.receive<List<LLMUsage>>()

                // 尝试从JWT令牌中提取用户ID
                val userId = call.principal<JWTPrincipal>()?.getClaim("userId", String::class)
                    ?: call.principal<JWTPrincipal>()?.getClaim("code", String::class)

                // 生成共享请求ID（如果未在第一条记录中提供）
                val sharedRequestId = usages.firstOrNull()?.requestId ?: UUID.randomUUID().toString()

                // 为每条记录添加用户ID和请求ID
                val enrichedUsages = usages.map { usage ->
                    usage.copy(
                        userId = usage.userId ?: userId,
                        requestId = usage.requestId ?: sharedRequestId
                    )
                }

                // 批量记录使用情况
                val inserted = usageService.recordBatch(enrichedUsages)
                call.respond(HttpStatusCode.Created, mapOf("insertedRows" to inserted))
            }
        }
    }
}
