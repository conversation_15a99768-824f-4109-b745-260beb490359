package com.dzhp.permit.routers.interal

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.dzhp.permit.models.StandardResponseStruct
import com.dzhp.permit.services.auth.AdminAuthService
import com.dzhp.permit.services.auth.AdminAuthResult
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.*
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 管理员登录路由
 * 支持LDAP和外部MySQL数据库两种认证方式
 */

@Serializable
data class AdminLoginRequest(
    val username: String,
    val password: String
)

@Serializable
data class AdminLoginResponse(
    val token: String,
    val expiresIn: Long
)

/**
 * 管理员登录路由
 *
 * - POST /api/admin/login : 管理员登录，支持LDAP和外部MySQL认证，返回 JWT token
 */
fun Application.adminLoginRouter() {
    val logger = LoggerFactory.getLogger("AdminLoginRouter")
    val adminAuthService = AdminAuthService(this)

    // JWT配置
    val jwtSecret = environment.config.property("jwt.secret").getString()
    val jwtIssuer = environment.config.property("jwt.domain").getString()
    val jwtAudience = environment.config.property("jwt.audience").getString()
    val jwtRealm = environment.config.property("jwt.realm").getString()

    routing {
        route("/api/admin") {
            post("/login") {
                try {
                    val loginRequest = call.receive<AdminLoginRequest>()
                    logger.info("管理员登录请求: ${loginRequest.username}")

                    // 使用认证服务进行验证
                    val authResult = adminAuthService.authenticate(loginRequest.username, loginRequest.password)

                    when (authResult) {
                        is AdminAuthResult.Success -> {
                            // 生成JWT token
                            val expiresIn = 24 * 60 * 60 * 1000L // 24小时
                            val expiresAt = Date(System.currentTimeMillis() + expiresIn)

                            val token = JWT.create()
                                .withAudience(jwtAudience)
                                .withIssuer(jwtIssuer)
                                .withClaim("role", "admin")
                                .withClaim("adminUsername", authResult.username)
                                .withExpiresAt(expiresAt)
                                .sign(Algorithm.HMAC256(jwtSecret))

                            // 返回成功响应
                            val response = AdminLoginResponse(token, expiresIn)
                            call.respond(
                                HttpStatusCode.OK,
                                StandardResponseStruct(
                                    code = HttpStatusCode.OK.value,
                                    message = "登录成功",
                                    data = JsonObject(mapOf(
                                        "token" to JsonPrimitive(response.token),
                                        "expiresIn" to JsonPrimitive(response.expiresIn)
                                    ))
                                )
                            )
                        }

                        is AdminAuthResult.UserNotFound -> {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "用户不存在",
                                    data = null
                                )
                            )
                        }

                        is AdminAuthResult.InvalidPassword -> {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "密码错误",
                                    data = null
                                )
                            )
                        }

                        is AdminAuthResult.AccountDisabled -> {
                            call.respond(
                                HttpStatusCode.Forbidden,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Forbidden.value,
                                    message = "账号已停用",
                                    data = null
                                )
                            )
                        }

                        is AdminAuthResult.ServiceError -> {
                            logger.error("认证服务错误: ${authResult.message}")
                            call.respond(
                                HttpStatusCode.InternalServerError,
                                StandardResponseStruct(
                                    code = HttpStatusCode.InternalServerError.value,
                                    message = "认证服务暂时不可用",
                                    data = null
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    // 处理异常
                    logger.error("管理员登录异常: ${e.message}", e)
                    call.respond(
                        HttpStatusCode.BadRequest,
                        StandardResponseStruct(
                            code = HttpStatusCode.BadRequest.value,
                            message = "请求处理失败: ${e.message}",
                            data = null
                        )
                    )
                }
            }
        }
    }
}
