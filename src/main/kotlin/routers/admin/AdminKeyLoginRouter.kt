package com.dzhp.permit.routers.admin

import com.dzhp.permit.models.EmptyResponseData
import com.dzhp.permit.models.standardResponse
import com.dzhp.permit.services.auth.AdminKeyLoginService
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable
import org.slf4j.LoggerFactory

/**
 * 管理员密钥登录请求
 */
@Serializable
data class AdminKeyLoginRequest(
    val adminKeyCode: String
)

/**
 * 管理员密钥登录响应
 */
@Serializable
data class AdminKeyLoginResponseData(
    val token: String,
    val expiresIn: Long,
    val id: Long,
    val code: String,
    val maxCNYLimit: Double,
    val status: String,
    val accountType: String,

    // 地域信息
    val province: String?,
    val city: String?,
    val district: String?,

    // 单位和联系信息
    val companyName: String,
    val contractNumber: String?,
    val contactPerson: String?,
    val contactPhone: String?,
    val contactEmail: String?,

    // 有效期
    val expiresAt: Long,

    val createBy: String?,
    val updateBy: String?,
    val deleted: Boolean,
    val createdAt: Long,
    val updatedAt: Long
)

/**
 * 管理员密钥登录路由
 *
 * - POST /api/admin-key/login : 使用管理员密钥登录，返回JWT token和管理员密钥信息
 */
fun Application.adminKeyLoginRouter(adminKeyLoginService: AdminKeyLoginService) {
    val logger = LoggerFactory.getLogger("AdminKeyLoginRouter")

    routing {
        route("/api/admin-key") {
            post("/login") {
                try {
                    val loginRequest = call.receive<AdminKeyLoginRequest>()

                    // 使用管理员密钥登录
                    val result = adminKeyLoginService.loginWithAdminKey(loginRequest.adminKeyCode)

                    when (result) {
                        is AdminKeyLoginService.LoginResult.Success -> {
                            // 登录成功，返回token和管理员密钥信息
                            val adminKey = result.adminKey
                            val responseData = AdminKeyLoginResponseData(
                                token = result.token,
                                expiresIn = result.expiresIn,
                                id = adminKey.id,
                                code = adminKey.code,
                                maxCNYLimit = adminKey.maxCNYLimit,
                                status = adminKey.status.toString(),
                                accountType = adminKey.accountType.toString(),

                                // 地域信息
                                province = adminKey.province,
                                city = adminKey.city,
                                district = adminKey.district,

                                // 单位和联系信息
                                companyName = adminKey.companyName,
                                contractNumber = adminKey.contractNumber,
                                contactPerson = adminKey.contactPerson,
                                contactPhone = adminKey.contactPhone,
                                contactEmail = adminKey.contactEmail,

                                // 有效期
                                expiresAt = adminKey.expiresAt,

                                createBy = adminKey.createBy,
                                updateBy = adminKey.updateBy,
                                deleted = adminKey.deleted,
                                createdAt = adminKey.createdAt,
                                updatedAt = adminKey.updatedAt
                            )

                            call.respond(
                                HttpStatusCode.OK,
                                standardResponse(
                                    code = HttpStatusCode.OK.value,
                                    message = "登录成功",
                                    data = responseData
                                )
                            )
                        }
                        AdminKeyLoginService.LoginResult.KeyNotFound -> {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "管理员密钥不存在"
                                )
                            )
                        }
                        AdminKeyLoginService.LoginResult.KeyDisabled -> {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "管理员密钥已停用"
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    logger.error("管理员密钥登录失败", e)
                    call.respond(
                        HttpStatusCode.BadRequest,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.BadRequest.value,
                            message = "无效请求: ${e.message}"
                        )
                    )
                }
            }
        }
    }
}
