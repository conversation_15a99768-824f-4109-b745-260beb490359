package com.dzhp.permit.routers.admin

import com.dzhp.permit.getWebSocketConnectionManager
import com.dzhp.permit.models.EmptyResponseData
import com.dzhp.permit.models.StandardResponseStruct
import com.dzhp.permit.services.InvitationCodeService
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.*
import org.slf4j.LoggerFactory

/**
 * 连接信息响应数据
 */
@Serializable
data class InvitationCodeConnectionInfo(
    val code: String,                // 邀请码
    val connectionCount: Int,        // 当前连接数
    val concurrentLimit: Int,        // 并发限制
    val id: Long,                    // 邀请码ID
    val userType: String,            // 用户类型
    val permissionLevel: String,     // 权限等级
    val province: String? = null,    // 省份
    val city: String? = null,        // 城市
    val district: String? = null,    // 区县
    val regionConstraint: String? = null, // 地域限制
    val status: String,              // 状态
    val remainingBalance: Double,    // 剩余余额
    val consumedAmount: Double,      // 已消耗金额
    val companyName: String? = null,   // 公司ID

    // 联系方式
    val contactPerson: String? = null, // 联系人姓名
    val contactPhone: String? = null,// 联系电话
    val contactEmail: String? = null,// 联系邮箱

    val expiresAt: Long              // 过期时间
)

/**
 * 连接列表响应数据
 */
@Serializable
data class ConnectionListResponse(
    val connections: List<InvitationCodeConnectionInfo>,
    val total: Int
)

/**
 * 管理员链接管理路由
 * 提供HTTP接口用于管理和监控WebSocket连接
 *
 * - GET /api/admin-key/current/linking : 获取当前管理员密钥创建的邀请码的连接情况
 * - POST /api/admin-key/kick/{invitationCode} : 踢出指定邀请码的所有连接
 */
fun Application.adminLinkingManageRouter(invitationCodeService: InvitationCodeService) {
    val webSocketConnectionManager = getWebSocketConnectionManager()
    val logger = LoggerFactory.getLogger("AdminLinkingManageRouter")

    routing {
        authenticate("admin-key-jwt") {
            route("/api/admin-key") {
                /**
                 * 获取当前管理员密钥创建的邀请码的连接情况
                 * 返回所有连接的二级邀请码的连接情况和基本信息
                 */
                get("/current/linking") {
                    try {
                        // 获取当前登录的管理员密钥信息
                        val principal = call.principal<JWTPrincipal>()
                        val adminKeyId = principal?.getClaim("adminKeyId", Long::class)

                        if (adminKeyId == null) {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "无效的管理员密钥认证",
                                    data = null
                                )
                            )
                            return@get
                        }

                        // 获取该管理员密钥创建的所有邀请码
                        val invitationCodes = invitationCodeService.findInvitationCodesByAdminKeyId(adminKeyId, false)

                        // 获取所有房间的连接统计
                        val roomStats = webSocketConnectionManager.getAllRoomStats()

                        // 构建连接信息列表
                        val connectionInfoList = invitationCodes.map { invitationCode ->
                            val connectionCount = roomStats[invitationCode.code] ?: 0

                            InvitationCodeConnectionInfo(
                                code = invitationCode.code,
                                connectionCount = connectionCount,
                                concurrentLimit = invitationCode.concurrentLimit,
                                id = invitationCode.id,
                                userType = invitationCode.userType.toString(),
                                permissionLevel = invitationCode.permissionLevel.toString(),
                                province = invitationCode.province,
                                city = invitationCode.city,
                                district = invitationCode.district,
                                regionConstraint = invitationCode.regionConstraint,
                                status = invitationCode.status.toString(),
                                remainingBalance = invitationCode.remainingBalance,
                                consumedAmount = invitationCode.consumedAmount,
                                companyName = invitationCode.companyName,
                                contactPerson = invitationCode.contactPerson,
                                contactPhone = invitationCode.contactPhone,
                                contactEmail = invitationCode.contactEmail,
                                expiresAt = invitationCode.expiresAt
                            )
                        }

                        // 返回连接信息
                        call.respond(
                            HttpStatusCode.OK,
                            StandardResponseStruct(
                                code = HttpStatusCode.OK.value,
                                message = "获取连接信息成功",
                                data = ConnectionListResponse(
                                    connections = connectionInfoList,
                                    total = connectionInfoList.size
                                )
                            )
                        )
                    } catch (e: Exception) {
                        logger.error("获取连接信息失败: ${e.message}", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct<EmptyResponseData>(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "获取连接信息失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }

                /**
                 * 踢出指定邀请码的所有连接
                 * 关闭WebSocket连接并清空JWT
                 */
                post("/kick/{invitationCode}") {
                    try {
                        val invitationCode = call.parameters["invitationCode"]

                        if (invitationCode.isNullOrEmpty()) {
                            call.respond(
                                HttpStatusCode.BadRequest,
                                StandardResponseStruct<EmptyResponseData>(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "邀请码不能为空",
                                    data = null
                                )
                            )
                            return@post
                        }

                        // 获取邀请码信息
                        val invitationCodeInfo = invitationCodeService.findInvitationCodeByCode(invitationCode)
                        if (invitationCodeInfo == null) {
                            call.respond(
                                HttpStatusCode.NotFound,
                                StandardResponseStruct<EmptyResponseData>(
                                    code = HttpStatusCode.NotFound.value,
                                    message = "邀请码不存在",
                                    data = null
                                )
                            )
                            return@post
                        }

                        // 获取当前登录的管理员密钥信息
                        val principal = call.principal<JWTPrincipal>()
                        val adminKeyId = principal?.getClaim("adminKeyId", Long::class)

                        // 验证邀请码是否属于当前管理员密钥
                        if (invitationCodeInfo.adminKeyId != adminKeyId) {
                            call.respond(
                                HttpStatusCode.Forbidden,
                                StandardResponseStruct<EmptyResponseData>(
                                    code = HttpStatusCode.Forbidden.value,
                                    message = "无权操作该邀请码",
                                    data = null
                                )
                            )
                            return@post
                        }

                        // 关闭该邀请码的所有WebSocket连接
                        val kickedCount = webSocketConnectionManager.kickConnectionsByInvitationCode(invitationCode)

                        call.respond(
                            HttpStatusCode.OK,
                            StandardResponseStruct(
                                code = HttpStatusCode.OK.value,
                                message = "成功踢出 $kickedCount 个连接",
                                data = JsonObject(mapOf(
                                    "kickedCount" to JsonPrimitive(kickedCount)
                                ))
                            )
                        )
                    } catch (e: Exception) {
                        logger.error("踢出连接失败: ${e.message}", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct<EmptyResponseData>(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "踢出连接失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }
            }
        }
    }
}
