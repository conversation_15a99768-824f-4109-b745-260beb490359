package com.dzhp.permit.routers.admin

import com.dzhp.permit.models.InvitationCode
import com.dzhp.permit.models.StandardResponseStruct
import com.dzhp.permit.services.AdminKeyService
import com.dzhp.permit.services.InvitationCodeService
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.*
import io.ktor.server.auth.authenticate
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.principal
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.json.*
import org.slf4j.LoggerFactory

/**
 * 管理员密钥用户管理邀请码路由
 *
 * - POST /api/admin-key/invitation/code/new: 创建新的二级邀请码
 * - GET /api/admin-key/invitation/code/list: 获取当前管理员密钥创建的邀请码列表
 * - PUT /api/admin-key/invitation/code/{id}: 修改二级邀请码
 * - DELETE /api/admin-key/invitation/code/{id}: 删除二级邀请码
 */
fun Application.adminKeyInvitationCodeRouter(
    invitationCodeService: InvitationCodeService,
    adminKeyService: AdminKeyService
) {
    val logger = LoggerFactory.getLogger("AdminKeyInvitationCodeRouter")

    routing {
        authenticate("admin-key-jwt") {
            route("/api/admin-key/invitation/code") {
                /** 创建新的二级邀请码 */
                post("/new") {
                    try {
                        // 获取当前登录的管理员密钥信息
                        val principal = call.principal<JWTPrincipal>()
                        val adminKeyId = principal?.getClaim("adminKeyId", Long::class)
                        val adminKeyCode = principal?.getClaim("adminKeyCode", String::class)

                        if (adminKeyId == null || adminKeyCode == null) {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "无效的管理员密钥认证",
                                    data = null
                                )
                            )
                            return@post
                        }

                        // 验证管理员密钥是否存在
                        val adminKey = adminKeyService.findAdminKeyById(adminKeyId)
                        if (adminKey == null) {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "管理员密钥不存在",
                                    data = null
                                )
                            )
                            return@post
                        }

                        // 接收邀请码请求
                        val invitationCode = call.receive<InvitationCode>()

                        // 强制使用管理员密钥的省份和城市，区县可以自定义
                        val adminProvince = adminKey.province ?: "上海市"
                        val adminCity = adminKey.city ?: "上海市"

                        // 获取区县参数，允许自定义
                        val requestDistrict = call.request.queryParameters["district"] ?: invitationCode.district.takeIf { it!!.isNotBlank() }

                        // 如果用户提交的省份或城市与管理员密钥不一致，记录警告日志
                        val requestProvince = call.request.queryParameters["province"] ?: invitationCode.province.takeIf { it!!.isNotBlank() }
                        val requestCity = call.request.queryParameters["city"] ?: invitationCode.city.takeIf { it!!.isNotBlank() }

                        if (requestProvince != null && requestProvince != adminProvince) {
                            logger.warn("用户尝试创建与管理员密钥省份不一致的二级邀请码: 用户提交省份=$requestProvince, 管理员密钥省份=$adminProvince")
                        }

                        if (requestCity != null && requestCity != adminCity) {
                            logger.warn("用户尝试创建与管理员密钥城市不一致的二级邀请码: 用户提交城市=$requestCity, 管理员密钥城市=$adminCity")
                        }

                        // 验证区县是否属于指定的省市
                        val district = requestDistrict
                        if (district != null) {
                            // 查询区县是否存在于指定的省市
                            val districtInfo = invitationCodeService.districtService.searchDistrictByRegion(adminProvince, adminCity, district)
                            if (districtInfo.isEmpty()) {
                                logger.warn("用户提交的区县不属于指定的省市: 省=$adminProvince, 市=$adminCity, 区=$district")

                                // 直接返回错误，不允许创建无效区县的邀请码
                                call.respond(
                                    HttpStatusCode.BadRequest,
                                    StandardResponseStruct(
                                        code = HttpStatusCode.BadRequest.value,
                                        message = "无效的区县信息，请选择属于 $adminProvince $adminCity 的区县",
                                        data = null
                                    )
                                )
                                return@post
                            }
                        } else {
                            // 如果没有提供区县，返回错误，要求用户提供有效的区县
                            call.respond(
                                HttpStatusCode.BadRequest,
                                StandardResponseStruct(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "请提供属于 $adminProvince $adminCity 的有效区县信息",
                                    data = null
                                )
                            )
                            return@post
                        }

                        // 强制设置管理员密钥ID和地区信息
                        val updatedInvitationCode = invitationCode.copy(
                            adminKeyId = adminKeyId,
                            // 强制使用管理员密钥的省份和城市
                            province = adminProvince,
                            city = adminCity,
                            // 使用验证后的区县
                            district = district,
                            // 始终生成随机邀请码，无需客户端传入code字段
                            code = invitationCodeService.generateRandomCode(adminKeyId, adminProvince, adminCity, district),
                            createBy = adminKeyCode,
                            updateBy = adminKeyCode,
                            createdAt = System.currentTimeMillis(),
                            updateAt = System.currentTimeMillis()
                        )

                        // 创建邀请码
                        val id = invitationCodeService.createInvitationCode(updatedInvitationCode, adminKeyCode)

                        // 返回成功响应
                        call.respond(
                            HttpStatusCode.Created,
                            StandardResponseStruct(
                                code = HttpStatusCode.Created.value,
                                message = "邀请码创建成功",
                                data = JsonObject(mapOf(
                                    "id" to JsonPrimitive(id),
                                    "code" to JsonPrimitive(updatedInvitationCode.code)
                                ))
                            )
                        )
                    } catch (e: Exception) {
                        logger.error("创建邀请码失败", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "创建邀请码失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }

                /** 获取当前管理员密钥创建的邀请码列表 */
                get("/list") {
                    try {
                        // 获取当前登录的管理员密钥信息
                        val principal = call.principal<JWTPrincipal>()
                        val adminKeyId = principal?.getClaim("adminKeyId", Long::class)

                        if (adminKeyId == null) {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "无效的管理员密钥认证",
                                    data = null
                                )
                            )
                            return@get
                        }

                        // 获取查询参数
                        val onlyActive = call.request.queryParameters["onlyActive"]?.toBoolean() ?: true

                        // 获取邀请码列表
                        val invitationCodes = invitationCodeService.findInvitationCodesByAdminKeyId(adminKeyId, onlyActive)

                        // 转换为JSON数组
                        val jsonArray = JsonArray(invitationCodes.map { code ->
                            JsonObject(mapOf(
                                "id" to JsonPrimitive(code.id),
                                "code" to JsonPrimitive(code.code),
                                "adminKeyId" to (code.adminKeyId?.let { JsonPrimitive(it) } ?: JsonNull),
                                "userType" to JsonPrimitive(code.userType.toString()),
                                "permissionLevel" to JsonPrimitive(code.permissionLevel.toString()),
                                "province" to (code.province?.let { JsonPrimitive(it) } ?: JsonNull),
                                "city" to (code.city?.let { JsonPrimitive(it) } ?: JsonNull),
                                "district" to (code.district?.let { JsonPrimitive(it) } ?: JsonNull),
                                "regionConstraint" to (code.regionConstraint?.let { JsonPrimitive(it) } ?: JsonNull),
                                "status" to JsonPrimitive(code.status.toString()),
                                "remainingBalance" to JsonPrimitive(code.remainingBalance),
                                "consumedAmount" to JsonPrimitive(code.consumedAmount),
                                "concurrentLimit" to JsonPrimitive(code.concurrentLimit),
                                "companyName" to (code.companyName?.let { JsonPrimitive(it) } ?: JsonNull),

                                // 联系方式
                                "contactPerson" to (code.contactPerson?.let { JsonPrimitive(it) } ?: JsonNull),
                                "contactPhone" to (code.contactPhone?.let { JsonPrimitive(it) } ?: JsonNull),
                                "contactEmail" to (code.contactEmail?.let { JsonPrimitive(it) } ?: JsonNull),

                                "createBy" to (code.createBy?.let { JsonPrimitive(it) } ?: JsonNull),
                                "updateBy" to (code.updateBy?.let { JsonPrimitive(it) } ?: JsonNull),
                                "deleted" to JsonPrimitive(code.deleted),
                                "createdAt" to JsonPrimitive(code.createdAt),
                                "expiresAt" to JsonPrimitive(code.expiresAt),
                                "updateAt" to JsonPrimitive(code.updateAt)
                            ))
                        })

                        // 返回结果
                        call.respond(
                            HttpStatusCode.OK,
                            StandardResponseStruct(
                                code = HttpStatusCode.OK.value,
                                message = "OK",
                                data = jsonArray
                            )
                        )
                    } catch (e: Exception) {
                        logger.error("获取邀请码列表失败", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "获取邀请码列表失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }

                /** 修改二级邀请码 */
                put("/{id}") {
                    try {
                        // 获取当前登录的管理员密钥信息
                        val principal = call.principal<JWTPrincipal>()
                        val adminKeyId = principal?.getClaim("adminKeyId", Long::class)
                        val adminKeyCode = principal?.getClaim("adminKeyCode", String::class)

                        if (adminKeyId == null || adminKeyCode == null) {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "无效的管理员密钥认证",
                                    data = null
                                )
                            )
                            return@put
                        }

                        // 获取路径参数
                        val id = call.parameters["id"]?.toLongOrNull()
                        if (id == null) {
                            call.respond(
                                HttpStatusCode.BadRequest,
                                StandardResponseStruct(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "无效的邀请码ID",
                                    data = null
                                )
                            )
                            return@put
                        }

                        // 检查邀请码是否存在
                        val existingInvitationCode = invitationCodeService.findInvitationCodeById(id)
                        if (existingInvitationCode == null) {
                            call.respond(
                                HttpStatusCode.NotFound,
                                StandardResponseStruct(
                                    code = HttpStatusCode.NotFound.value,
                                    message = "邀请码不存在",
                                    data = null
                                )
                            )
                            return@put
                        }

                        // 检查邀请码是否属于当前管理员密钥
                        if (!invitationCodeService.isInvitationCodeOwnedByAdminKey(id, adminKeyId)) {
                            call.respond(
                                HttpStatusCode.Forbidden,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Forbidden.value,
                                    message = "无权修改此邀请码",
                                    data = null
                                )
                            )
                            return@put
                        }

                        // 接收更新请求
                        val updates = call.receive<InvitationCode>()

                        // 获取管理员密钥信息
                        val adminKey = adminKeyService.findAdminKeyById(adminKeyId)
                        if (adminKey == null) {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "管理员密钥不存在",
                                    data = null
                                )
                            )
                            return@put
                        }

                        // 强制使用管理员密钥的省份和城市
                        val adminProvince = adminKey.province ?: "上海市"
                        val adminCity = adminKey.city ?: "上海市"

                        // 如果用户提交的省份或城市与管理员密钥不一致，记录警告日志
                        if (updates.province != null && updates.province != adminProvince) {
                            logger.warn("用户尝试修改二级邀请码为与管理员密钥省份不一致: 用户提交省份=${updates.province}, 管理员密钥省份=$adminProvince")
                        }

                        if (updates.city != null && updates.city != adminCity) {
                            logger.warn("用户尝试修改二级邀请码为与管理员密钥城市不一致: 用户提交城市=${updates.city}, 管理员密钥城市=$adminCity")
                        }

                        // 验证区县是否属于指定的省市
                        var district = updates.district
                        if (district != null) {
                            // 查询区县是否存在于指定的省市
                            val districtInfo = invitationCodeService.districtService.searchDistrictByRegion(adminProvince, adminCity, district)
                            if (districtInfo.isEmpty()) {
                                logger.warn("用户尝试修改二级邀请码为与管理员密钥区县不一致: 省=$adminProvince, 市=$adminCity, 区=$district")

                                // 直接返回错误，不允许修改为无效区县
                                call.respond(
                                    HttpStatusCode.BadRequest,
                                    StandardResponseStruct(
                                        code = HttpStatusCode.BadRequest.value,
                                        message = "无效的区县信息，请选择属于 $adminProvince $adminCity 的区县",
                                        data = null
                                    )
                                )
                                return@put
                            }
                        } else {
                            // 如果没有提供区县，保持原有区县
                            district = existingInvitationCode.district
                        }

                        // 创建更新对象，保留不可修改的字段，强制使用管理员密钥的省份和城市
                        val updatedInvitationCode = existingInvitationCode.copy(
                            userType = updates.userType,
                            permissionLevel = updates.permissionLevel,
                            // 强制使用管理员密钥的省份和城市
                            province = adminProvince,
                            city = adminCity,
                            // 使用验证后的区县
                            district = district,
                            regionConstraint = updates.regionConstraint,
                            status = updates.status,
                            remainingBalance = updates.remainingBalance,
                            consumedAmount = updates.consumedAmount,
                            concurrentLimit = updates.concurrentLimit,
                            companyName = updates.companyName,

                            // 联系方式
                            contactPerson = updates.contactPerson,
                            contactPhone = updates.contactPhone,
                            contactEmail = updates.contactEmail,

                            expiresAt = updates.expiresAt,
                            updateBy = adminKeyCode,
                            updateAt = System.currentTimeMillis()
                        )

                        // 更新邀请码
                        val success = invitationCodeService.updateInvitationCode(id, updatedInvitationCode, adminKeyCode)

                        if (success) {
                            call.respond(
                                HttpStatusCode.OK,
                                StandardResponseStruct(
                                    code = HttpStatusCode.OK.value,
                                    message = "邀请码更新成功",
                                    data = null
                                )
                            )
                        } else {
                            call.respond(
                                HttpStatusCode.InternalServerError,
                                StandardResponseStruct(
                                    code = HttpStatusCode.InternalServerError.value,
                                    message = "邀请码更新失败",
                                    data = null
                                )
                            )
                        }
                    } catch (e: Exception) {
                        logger.error("更新邀请码失败", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "更新邀请码失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }

                /** 删除二级邀请码 */
                delete("/{id}") {
                    try {
                        // 获取当前登录的管理员密钥信息
                        val principal = call.principal<JWTPrincipal>()
                        val adminKeyId = principal?.getClaim("adminKeyId", Long::class)
                        val adminKeyCode = principal?.getClaim("adminKeyCode", String::class)

                        if (adminKeyId == null || adminKeyCode == null) {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "无效的管理员密钥认证",
                                    data = null
                                )
                            )
                            return@delete
                        }

                        // 获取路径参数
                        val id = call.parameters["id"]?.toLongOrNull()
                        if (id == null) {
                            call.respond(
                                HttpStatusCode.BadRequest,
                                StandardResponseStruct(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "无效的邀请码ID",
                                    data = null
                                )
                            )
                            return@delete
                        }

                        // 检查邀请码是否存在
                        val existingInvitationCode = invitationCodeService.findInvitationCodeById(id)
                        if (existingInvitationCode == null) {
                            call.respond(
                                HttpStatusCode.NotFound,
                                StandardResponseStruct(
                                    code = HttpStatusCode.NotFound.value,
                                    message = "邀请码不存在",
                                    data = null
                                )
                            )
                            return@delete
                        }

                        // 检查邀请码是否属于当前管理员密钥
                        if (!invitationCodeService.isInvitationCodeOwnedByAdminKey(id, adminKeyId)) {
                            call.respond(
                                HttpStatusCode.Forbidden,
                                StandardResponseStruct(
                                    code = HttpStatusCode.Forbidden.value,
                                    message = "无权删除此邀请码",
                                    data = null
                                )
                            )
                            return@delete
                        }

                        // 删除邀请码
                        val success = invitationCodeService.deleteInvitationCode(id, adminKeyCode)

                        if (success) {
                            call.respond(
                                HttpStatusCode.OK,
                                StandardResponseStruct(
                                    code = HttpStatusCode.OK.value,
                                    message = "邀请码删除成功",
                                    data = null
                                )
                            )
                        } else {
                            call.respond(
                                HttpStatusCode.InternalServerError,
                                StandardResponseStruct(
                                    code = HttpStatusCode.InternalServerError.value,
                                    message = "邀请码删除失败",
                                    data = null
                                )
                            )
                        }
                    } catch (e: Exception) {
                        logger.error("删除邀请码失败", e)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            StandardResponseStruct(
                                code = HttpStatusCode.InternalServerError.value,
                                message = "删除邀请码失败: ${e.message}",
                                data = null
                            )
                        )
                    }
                }
            }
        }
    }
}
