package com.dzhp.permit.routers.auth.mobile

import com.dzhp.permit.services.auth.OAuthRefreshResult
import com.dzhp.permit.services.auth.OAuthResult
import com.dzhp.permit.services.auth.UserMobileOAuthService
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.auth.authentication
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable

/**
 * OAuth令牌请求
 */
@Serializable
data class OAuthTokenRequest(
    val grantType: String,
    val mobile: String? = null,
    val verificationCode: String? = null,
    val refreshToken: String? = null,
    val scope: String? = null,
    val clientId: String? = null,
    val clientSecret: String? = null
)

/**
 * OAuth令牌响应
 */
@Serializable
data class OAuthTokenResponse(
    val accessToken: String,
    val tokenType: String,
    val expiresIn: Int,
    val refreshToken: String? = null,
    val scope: String? = null,
    val idToken: String? = null
)

/**
 * 用户手机OAuth路由
 *
 * - POST /api/oauth/token : OAuth令牌端点，支持手机验证码授权和刷新令牌
 * - GET /api/oauth/userinfo : 获取用户信息
 */
fun Application.userMobileOAuthRouter(
    oauthService: UserMobileOAuthService
) {
    routing {
        route("/api/oauth") {
            /**
             * OAuth令牌端点
             * 支持手机验证码授权和刷新令牌
             */
            post("/token") {
                try {
                    val request = call.receive<OAuthTokenRequest>()
                    
                    when (request.grantType) {
                        "mobile_verification_code" -> {
                            // 验证必要参数
                            if (request.mobile.isNullOrEmpty() || request.verificationCode.isNullOrEmpty()) {
                                call.respond(
                                    HttpStatusCode.BadRequest,
                                    mapOf(
                                        "error" to "invalid_request",
                                        "error_description" to "Missing required parameters: mobile and verification_code"
                                    )
                                )
                                return@post
                            }
                            
                            // 验证手机号格式
                            if (!isValidMobile(request.mobile)) {
                                call.respond(
                                    HttpStatusCode.BadRequest,
                                    mapOf(
                                        "error" to "invalid_request",
                                        "error_description" to "Invalid mobile format"
                                    )
                                )
                                return@post
                            }
                            
                            // 使用手机号和验证码进行OAuth认证
                            val result = oauthService.authenticateWithMobileAndCode(
                                request.mobile,
                                request.verificationCode
                            )
                            
                            when (result) {
                                is OAuthResult.Success -> {
                                    call.respond(
                                        HttpStatusCode.OK,
                                        OAuthTokenResponse(
                                            accessToken = result.accessToken,
                                            tokenType = result.tokenType,
                                            expiresIn = result.expiresIn,
                                            refreshToken = result.refreshToken,
                                            scope = result.scope
                                        )
                                    )
                                }
                                OAuthResult.UserNotFound -> {
                                    call.respond(
                                        HttpStatusCode.BadRequest,
                                        mapOf(
                                            "error" to "invalid_grant",
                                            "error_description" to "User not found"
                                        )
                                    )
                                }
                                OAuthResult.InvalidVerificationCode -> {
                                    call.respond(
                                        HttpStatusCode.BadRequest,
                                        mapOf(
                                            "error" to "invalid_grant",
                                            "error_description" to "Invalid verification code"
                                        )
                                    )
                                }
                            }
                        }
                        "refresh_token" -> {
                            // 验证必要参数
                            if (request.refreshToken.isNullOrEmpty()) {
                                call.respond(
                                    HttpStatusCode.BadRequest,
                                    mapOf(
                                        "error" to "invalid_request",
                                        "error_description" to "Missing required parameter: refresh_token"
                                    )
                                )
                                return@post
                            }
                            
                            // 刷新令牌
                            val result = oauthService.refreshToken(request.refreshToken)
                            
                            when (result) {
                                is OAuthRefreshResult.Success -> {
                                    call.respond(
                                        HttpStatusCode.OK,
                                        OAuthTokenResponse(
                                            accessToken = result.accessToken,
                                            tokenType = result.tokenType,
                                            expiresIn = result.expiresIn,
                                            refreshToken = null,
                                            scope = request.scope
                                        )
                                    )
                                }
                                OAuthRefreshResult.InvalidToken -> {
                                    call.respond(
                                        HttpStatusCode.BadRequest,
                                        mapOf(
                                            "error" to "invalid_grant",
                                            "error_description" to "Invalid refresh token"
                                        )
                                    )
                                }
                            }
                        }
                        else -> {
                            call.respond(
                                HttpStatusCode.BadRequest,
                                mapOf(
                                    "error" to "unsupported_grant_type",
                                    "error_description" to "Unsupported grant type: ${request.grantType}"
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        mapOf(
                            "error" to "server_error",
                            "error_description" to "An error occurred: ${e.message}"
                        )
                    )
                }
            }
            
            /**
             * 获取用户信息
             * 需要在Authorization头中提供访问令牌
             */
            get("/userinfo") {
                val authHeader = call.request.headers["Authorization"]
                if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                    call.respond(
                        HttpStatusCode.Unauthorized,
                        mapOf(
                            "error" to "invalid_token",
                            "error_description" to "Missing or invalid Authorization header"
                        )
                    )
                    return@get
                }
                
                val token = authHeader.substring(7)
                
                try {
                    // 验证令牌并获取用户信息
                    // 这里简化处理，实际应该从令牌中解析用户ID并查询数据库
                    val principal = call.authentication.principal<JWTPrincipal>()
                    if (principal == null) {
                        call.respond(
                            HttpStatusCode.Unauthorized,
                            mapOf(
                                "error" to "invalid_token",
                                "error_description" to "Invalid token"
                            )
                        )
                        return@get
                    }
                    
                    val userId = principal.payload.getClaim("userId").asLong()
                    val mobile = principal.payload.getClaim("mobile").asString()
                    val userType = principal.payload.getClaim("userType").asString()
                    val permissionLevel = principal.payload.getClaim("permissionLevel").asString()
                    
                    call.respond(
                        HttpStatusCode.OK,
                        mapOf(
                            "sub" to userId.toString(),
                            "mobile" to (mobile ?: ""),
                            "user_type" to (userType ?: ""),
                            "permission_level" to (permissionLevel ?: "")
                        )
                    )
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.Unauthorized,
                        mapOf(
                            "error" to "invalid_token",
                            "error_description" to "Invalid token: ${e.message}"
                        )
                    )
                }
            }
        }
    }
}

/**
 * 验证手机号格式
 *
 * @param mobile 手机号
 * @return 是否是有效的手机号
 */
private fun isValidMobile(mobile: String): Boolean {
    // 中国大陆手机号格式验证：1开头的11位数字
    return mobile.matches(Regex("^1\\d{10}$"))
}
