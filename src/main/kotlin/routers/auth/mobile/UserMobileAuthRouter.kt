package com.dzhp.permit.routers.auth.mobile

import com.dzhp.permit.models.EmptyResponseData
import com.dzhp.permit.models.LoginResponseData
import com.dzhp.permit.models.standardResponse
import com.dzhp.permit.services.auth.LoginResult
import com.dzhp.permit.services.auth.RegistrationResult
import com.dzhp.permit.services.auth.UserMobileLoginService
import com.dzhp.permit.services.auth.UserMobileRegistrationService
import com.dzhp.permit.services.auth.UserMobileVerificationService
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable

/**
 * 发送验证码请求
 */
@Serializable
data class SendVerificationCodeRequest(
    val mobile: String
)

/**
 * 验证码登录请求
 */
@Serializable
data class MobileVerificationLoginRequest(
    val mobile: String,
    val verificationCode: String
)

/**
 * 密码登录请求
 */
@Serializable
data class MobilePasswordLoginRequest(
    val mobile: String,
    val password: String
)

/**
 * 手机注册请求
 */
@Serializable
data class MobileRegistrationRequest(
    val mobile: String,
    val verificationCode: String,
    val password: String? = null
)

/**
 * 用户手机认证路由
 *
 * - POST /api/user/mobile/send-code : 发送手机验证码
 * - POST /api/user/mobile/register : 使用手机号和验证码注册
 * - POST /api/user/mobile/login/code : 使用手机号和验证码登录
 * - POST /api/user/mobile/login/password : 使用手机号和密码登录
 */
fun Application.userMobileAuthRouter(
    verificationService: UserMobileVerificationService,
    registrationService: UserMobileRegistrationService,
    loginService: UserMobileLoginService
) {
    routing {
        route("/api/user/mobile") {
            /**
             * 发送手机验证码
             */
            post("/send-code") {
                try {
                    val request = call.receive<SendVerificationCodeRequest>()
                    
                    // 验证手机号格式
                    if (!isValidMobile(request.mobile)) {
                        call.respond(
                            HttpStatusCode.BadRequest,
                            standardResponse<EmptyResponseData>(
                                code = HttpStatusCode.BadRequest.value,
                                message = "无效的手机号格式"
                            )
                        )
                        return@post
                    }
                    
                    // 生成并发送验证码
                    verificationService.generateAndStoreVerificationCode(request.mobile)
                    
                    call.respond(
                        HttpStatusCode.OK,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.OK.value,
                            message = "验证码已发送"
                        )
                    )
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.InternalServerError.value,
                            message = "发送验证码失败: ${e.message}"
                        )
                    )
                }
            }
            
            /**
             * 使用手机号和验证码注册
             */
            post("/register") {
                try {
                    val request = call.receive<MobileRegistrationRequest>()
                    
                    // 验证手机号格式
                    if (!isValidMobile(request.mobile)) {
                        call.respond(
                            HttpStatusCode.BadRequest,
                            standardResponse<EmptyResponseData>(
                                code = HttpStatusCode.BadRequest.value,
                                message = "无效的手机号格式"
                            )
                        )
                        return@post
                    }
                    
                    // 注册用户
                    val result = registrationService.registerWithMobileAndCode(
                        request.mobile,
                        request.verificationCode,
                        request.password
                    )
                    
                    when (result) {
                        is RegistrationResult.Success -> {
                            // 注册成功后自动登录
                            val loginResult = loginService.loginWithMobileAndCode(
                                request.mobile,
                                request.verificationCode
                            )
                            
                            when (loginResult) {
                                is LoginResult.Success -> {
                                    val responseData = LoginResponseData(
                                        token = loginResult.token,
                                        expiresIn = loginResult.expiresIn,
                                        userType = loginResult.userType.toString(),
                                        permissionLevel = loginResult.permissionLevel
                                    )
                                    
                                    call.respond(
                                        HttpStatusCode.Created,
                                        standardResponse(
                                            code = HttpStatusCode.Created.value,
                                            message = "注册成功",
                                            data = responseData
                                        )
                                    )
                                }
                                else -> {
                                    // 注册成功但登录失败的情况
                                    call.respond(
                                        HttpStatusCode.Created,
                                        standardResponse<EmptyResponseData>(
                                            code = HttpStatusCode.Created.value,
                                            message = "注册成功，请登录"
                                        )
                                    )
                                }
                            }
                        }
                        RegistrationResult.InvalidVerificationCode -> {
                            call.respond(
                                HttpStatusCode.BadRequest,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "验证码无效或已过期"
                                )
                            )
                        }
                        RegistrationResult.MobileAlreadyRegistered -> {
                            call.respond(
                                HttpStatusCode.Conflict,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.Conflict.value,
                                    message = "该手机号已注册"
                                )
                            )
                        }
                        is RegistrationResult.SystemError -> {
                            call.respond(
                                HttpStatusCode.InternalServerError,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.InternalServerError.value,
                                    message = "注册失败: ${result.message}"
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.InternalServerError.value,
                            message = "注册失败: ${e.message}"
                        )
                    )
                }
            }
            
            /**
             * 使用手机号和验证码登录
             */
            post("/login/code") {
                try {
                    val request = call.receive<MobileVerificationLoginRequest>()
                    
                    // 验证手机号格式
                    if (!isValidMobile(request.mobile)) {
                        call.respond(
                            HttpStatusCode.BadRequest,
                            standardResponse<EmptyResponseData>(
                                code = HttpStatusCode.BadRequest.value,
                                message = "无效的手机号格式"
                            )
                        )
                        return@post
                    }
                    
                    // 登录
                    val result = loginService.loginWithMobileAndCode(
                        request.mobile,
                        request.verificationCode
                    )
                    
                    when (result) {
                        is LoginResult.Success -> {
                            val responseData = LoginResponseData(
                                token = result.token,
                                expiresIn = result.expiresIn,
                                userType = result.userType.toString(),
                                permissionLevel = result.permissionLevel
                            )
                            
                            call.respond(
                                HttpStatusCode.OK,
                                standardResponse(
                                    code = HttpStatusCode.OK.value,
                                    message = "登录成功",
                                    data = responseData
                                )
                            )
                        }
                        LoginResult.UserNotFound -> {
                            call.respond(
                                HttpStatusCode.NotFound,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.NotFound.value,
                                    message = "用户不存在"
                                )
                            )
                        }
                        LoginResult.InvalidVerificationCode -> {
                            call.respond(
                                HttpStatusCode.BadRequest,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "验证码无效或已过期"
                                )
                            )
                        }
                        else -> {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "登录失败"
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.InternalServerError.value,
                            message = "登录失败: ${e.message}"
                        )
                    )
                }
            }
            
            /**
             * 使用手机号和密码登录
             */
            post("/login/password") {
                try {
                    val request = call.receive<MobilePasswordLoginRequest>()
                    
                    // 验证手机号格式
                    if (!isValidMobile(request.mobile)) {
                        call.respond(
                            HttpStatusCode.BadRequest,
                            standardResponse<EmptyResponseData>(
                                code = HttpStatusCode.BadRequest.value,
                                message = "无效的手机号格式"
                            )
                        )
                        return@post
                    }
                    
                    // 登录
                    val result = loginService.loginWithMobileAndPassword(
                        request.mobile,
                        request.password
                    )
                    
                    when (result) {
                        is LoginResult.Success -> {
                            val responseData = LoginResponseData(
                                token = result.token,
                                expiresIn = result.expiresIn,
                                userType = result.userType.toString(),
                                permissionLevel = result.permissionLevel
                            )
                            
                            call.respond(
                                HttpStatusCode.OK,
                                standardResponse(
                                    code = HttpStatusCode.OK.value,
                                    message = "登录成功",
                                    data = responseData
                                )
                            )
                        }
                        LoginResult.UserNotFound -> {
                            call.respond(
                                HttpStatusCode.NotFound,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.NotFound.value,
                                    message = "用户不存在"
                                )
                            )
                        }
                        LoginResult.InvalidCredentials -> {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "密码错误"
                                )
                            )
                        }
                        else -> {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "登录失败"
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.InternalServerError.value,
                            message = "登录失败: ${e.message}"
                        )
                    )
                }
            }
        }
    }
}

/**
 * 验证手机号格式
 *
 * @param mobile 手机号
 * @return 是否是有效的手机号
 */
private fun isValidMobile(mobile: String): Boolean {
    // 中国大陆手机号格式验证：1开头的11位数字
    return mobile.matches(Regex("^1\\d{10}$"))
}
