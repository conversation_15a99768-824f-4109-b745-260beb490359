package com.dzhp.permit.routers.auth.wechat

import com.dzhp.permit.models.EmptyResponseData
import com.dzhp.permit.models.standardResponse
import com.dzhp.permit.services.auth.UserWeChatLoginService
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable

/**
 * 微信登录请求
 */
@Serializable
data class WeChatLoginRequest(
    val code: String,
    val state: String? = null
)

/**
 * 微信登录响应
 */
@Serializable
data class WeChatLoginResponseData(
    val token: String,
    val expiresIn: Long,
    val openId: String,
    val unionId: String?,
    val userType: String,
    val permissionLevel: String
)

/**
 * 用户微信登录路由
 *
 * - POST /api/user/wechat/login : 用户使用微信授权码登录，返回JWT token
 */
fun Application.userWeChatLoginRouter(userWeChatLoginService: UserWeChatLoginService) {
    routing {
        route("/api/user") {
            post("/wechat/login") {
                try {
                    val loginRequest = call.receive<WeChatLoginRequest>()
                    
                    // 使用微信授权码登录
                    val result = userWeChatLoginService.loginWithCode(loginRequest.code)
                    
                    when (result) {
                        is UserWeChatLoginService.WeChatLoginResult.Success -> {
                            // 登录成功，返回token和用户信息
                            val responseData = WeChatLoginResponseData(
                                token = result.token,
                                expiresIn = result.expiresIn,
                                openId = result.openId,
                                unionId = result.unionId,
                                userType = result.userType.toString(),
                                permissionLevel = result.permissionLevel
                            )
                            
                            call.respond(
                                HttpStatusCode.OK,
                                standardResponse(
                                    code = HttpStatusCode.OK.value,
                                    message = "微信登录成功",
                                    data = responseData
                                )
                            )
                        }
                        
                        UserWeChatLoginService.WeChatLoginResult.InvalidCode -> {
                            call.respond(
                                HttpStatusCode.BadRequest,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "无效的授权码"
                                )
                            )
                        }
                        
                        is UserWeChatLoginService.WeChatLoginResult.ApiError -> {
                            call.respond(
                                HttpStatusCode.BadRequest,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.BadRequest.value,
                                    message = "微信API错误: ${result.errorCode} - ${result.errorMsg}"
                                )
                            )
                        }
                        
                        is UserWeChatLoginService.WeChatLoginResult.ServerError -> {
                            call.respond(
                                HttpStatusCode.InternalServerError,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.InternalServerError.value,
                                    message = "服务器内部错误: ${result.message}"
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    // 处理异常
                    call.respond(
                        HttpStatusCode.BadRequest,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.BadRequest.value,
                            message = "无效请求: ${e.message}"
                        )
                    )
                }
            }
        }
    }
}
