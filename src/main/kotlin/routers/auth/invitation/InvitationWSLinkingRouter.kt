package com.dzhp.permit.routers.auth.invitation

import com.dzhp.permit.getWebSocketConnectionManager
import com.dzhp.permit.services.InvitationCodeService
import io.ktor.server.application.*
import io.ktor.server.routing.*
import io.ktor.server.websocket.*
import io.ktor.websocket.*
import kotlinx.coroutines.channels.ClosedReceiveChannelException
import org.slf4j.LoggerFactory

/**
 * WebSocket路由
 * 提供WebSocket连接和监控的路由
 *
 * - WS /ws/user/connect/{invitationCode}?token={token} : 用户WebSocket连接，用于并发控制
 */
fun Application.webSocketRouter() {
    val webSocketConnectionManager = getWebSocketConnectionManager()
    val invitationCodeService = InvitationCodeService(this)

    routing {
        // 用户登录WebSocket连接
        // 路径格式: /ws/user/connect/{invitationCode}
        webSocket("/ws/user/connect/{invitationCode}") {
            val logger = LoggerFactory.getLogger("UserWebSocket")
            val invitationCode = call.parameters["invitationCode"]

            if (invitationCode.isNullOrEmpty()) {
                close(CloseReason(CloseReason.Codes.VIOLATED_POLICY, "邀请码不能为空"))
                return@webSocket
            }

            // 从查询参数中获取JWT令牌
            val token = call.request.queryParameters["token"]
            if (token.isNullOrEmpty()) {
                close(CloseReason(CloseReason.Codes.VIOLATED_POLICY, "令牌不能为空"))
                return@webSocket
            }

            try {
                logger.info("尝试建立WebSocket连接: 邀请码=$invitationCode, token=${token.take(20)}...")

                // 获取邀请码信息以检查并发限制
                val invitationCodeInfo = invitationCodeService.findInvitationCodeByCode(invitationCode)
                if (invitationCodeInfo == null) {
                    logger.error("WebSocket连接失败: 无效的邀请码")
                    close(CloseReason(CloseReason.Codes.VIOLATED_POLICY, "无效的邀请码"))
                    return@webSocket
                }

                // 检查并发连接数是否已达到限制
                val concurrentLimit = invitationCodeInfo.concurrentLimit
                val withinLimit = webSocketConnectionManager.checkConcurrencyLimit(invitationCode, concurrentLimit)

                if (!withinLimit) {
                    logger.error("WebSocket连接失败: 并发连接数已达到限制 ($concurrentLimit)")
                    close(CloseReason(CloseReason.Codes.TRY_AGAIN_LATER, "并发连接数已达到限制，请稍后再试"))
                    return@webSocket
                }

                // 添加连接到管理器
                val added = webSocketConnectionManager.addConnection(this, token)
                if (!added) {
                    logger.error("WebSocket连接失败: 无效的令牌")
                    close(CloseReason(CloseReason.Codes.VIOLATED_POLICY, "无效的令牌"))
                    return@webSocket
                }

                logger.info("WebSocket连接已建立: $invitationCode, 当前连接数: ${webSocketConnectionManager.getConnectionCount(invitationCode)}/$concurrentLimit")

                try {
                    // 保持连接直到客户端断开
                    for (frame in incoming) {
                        // 可以处理客户端发送的消息，但在这个实现中我们只需要保持连接
                        when (frame) {
                            is Frame.Text -> {
                                val text = frame.readText()
                                if (text == "ping") {
                                    outgoing.send(Frame.Text("pong"))
                                }
                            }
                            else -> {}
                        }
                    }
                } catch (e: ClosedReceiveChannelException) {
                    logger.info("WebSocket连接已关闭: $invitationCode")
                } catch (e: Throwable) {
                    logger.error("WebSocket错误: ${e.message}", e)
                } finally {
                    // 移除连接
                    webSocketConnectionManager.removeConnection(this)
                    logger.info("WebSocket连接已移除: $invitationCode, 剩余连接数: ${webSocketConnectionManager.getConnectionCount(invitationCode)}/$concurrentLimit")
                }
            } catch (e: Exception) {
                logger.error("处理WebSocket连接时出错: ${e.message}", e)
                close(CloseReason(CloseReason.Codes.INTERNAL_ERROR, "服务器内部错误"))
            }
        }
    }
}
