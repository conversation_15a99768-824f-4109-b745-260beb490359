package com.dzhp.permit.routers.auth.invitation

import com.dzhp.permit.models.EmptyResponseData
import com.dzhp.permit.models.InvitationLoginResponseData
import com.dzhp.permit.models.standardResponse
import com.dzhp.permit.services.auth.UserInvitationLoginService
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable

// TODO(实现个人用户微信登陆)

/**
 * 用户登录请求
 */
@Serializable
data class UserLoginRequest(
    val invitationCode: String
)

/**
 * 用户登录路由
 *
 * - POST /api/user/invitation/login : 用户使用邀请码登录，返回JWT token和邀请码的所有字段信息
 */
fun Application.userLoginRouter(userInvitationLoginService: UserInvitationLoginService) {
    routing {
        route("/api/user") {
            post("/invitation/login") {
                try {
                    val loginRequest = call.receive<UserLoginRequest>()

                    // 使用邀请码登录
                    val result = userInvitationLoginService.loginWithInvitationCode(loginRequest.invitationCode)

                    when (result) {
                        is UserInvitationLoginService.LoginResult.Success -> {
                            // 登录成功，返回token和邀请码完整信息
                            val invCode = result.invitationCode
                            val responseData = InvitationLoginResponseData(
                                // 令牌信息
                                token = result.token,
                                expiresIn = result.expiresIn,
                                is_admin = result.isAdmin,

                                // 邀请码完整信息
                                id = invCode.id,
                                code = invCode.code,
                                adminKeyId = invCode.adminKeyId,
                                userType = invCode.userType.toString(),
                                permissionLevel = invCode.permissionLevel.toString(),

                                // 地域限制（省市区三级）
                                province = invCode.province,
                                city = invCode.city,
                                district = invCode.district,

                                // 兼容旧版本
                                regionConstraint = invCode.regionConstraint,

                                status = invCode.status.toString(),
                                remainingBalance = invCode.remainingBalance,
                                consumedAmount = invCode.consumedAmount,
                                concurrentLimit = invCode.concurrentLimit,
                                companyName = invCode.companyName,

                                // 联系方式
                                contactPerson = invCode.contactPerson,
                                contactPhone = invCode.contactPhone,
                                contactEmail = invCode.contactEmail,

                                createBy = invCode.createBy,
                                updateBy = invCode.updateBy,
                                deleted = invCode.deleted,
                                createdAt = invCode.createdAt,
                                expiresAt = invCode.expiresAt,
                                updateAt = invCode.updateAt
                            )

                            call.respond(
                                HttpStatusCode.OK,
                                standardResponse(
                                    code = HttpStatusCode.OK.value,
                                    message = "Login successful",
                                    data = responseData
                                )
                            )
                        }
                        is UserInvitationLoginService.LoginResult.AdminKeySuccess -> {
                            // 管理员密钥登录成功，返回token和管理员密钥信息
                            val adminKey = result.adminKey

                            // 创建一个虚拟的邀请码响应，包含管理员密钥信息
                            val responseData = InvitationLoginResponseData(
                                // 令牌信息
                                token = result.token,
                                expiresIn = result.expiresIn,
                                is_admin = true,  // 标记为管理员

                                // 管理员密钥信息
                                id = adminKey.id,
                                code = adminKey.code,
                                adminKeyId = null,
                                userType = "INDIVIDUAL",
                                permissionLevel = "ADMIN",

                                // 地域限制（使用管理员密钥的地域信息）
                                province = adminKey.province,
                                city = adminKey.city,
                                district = adminKey.district,
                                regionConstraint = null,

                                status = adminKey.status.toString(),
                                remainingBalance = adminKey.maxCNYLimit,
                                consumedAmount = 0.0,  // 默认值
                                concurrentLimit = 10,  // 默认值
                                companyName = adminKey.companyName,

                                // 联系方式（使用管理员密钥的联系信息）
                                contactPerson = adminKey.contactPerson,
                                contactPhone = adminKey.contactPhone,
                                contactEmail = adminKey.contactEmail,

                                createBy = adminKey.createBy,
                                updateBy = adminKey.updateBy,
                                deleted = adminKey.deleted,
                                createdAt = adminKey.createdAt,
                                expiresAt = System.currentTimeMillis() + (365 * 24 * 60 * 60 * 1000L),  // 默认一年
                                updateAt = adminKey.updatedAt
                            )

                            call.respond(
                                HttpStatusCode.OK,
                                standardResponse(
                                    code = HttpStatusCode.OK.value,
                                    message = "Login successful",
                                    data = responseData
                                )
                            )
                        }
                        UserInvitationLoginService.LoginResult.CodeNotFound -> {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "Invalid invitation code"
                                )
                            )
                        }
                        UserInvitationLoginService.LoginResult.CodeInactive -> {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "Invitation code is inactive"
                                )
                            )
                        }
                        UserInvitationLoginService.LoginResult.CodeExpired -> {
                            call.respond(
                                HttpStatusCode.Unauthorized,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.Unauthorized.value,
                                    message = "Invitation code has expired"
                                )
                            )
                        }
                        UserInvitationLoginService.LoginResult.InsufficientBalance -> {
                            call.respond(
                                HttpStatusCode.PaymentRequired,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.PaymentRequired.value,
                                    message = "Invitation code has insufficient balance"
                                )
                            )
                        }
                        UserInvitationLoginService.LoginResult.ConcurrencyLimitExceeded -> {
                            call.respond(
                                HttpStatusCode.TooManyRequests,
                                standardResponse<EmptyResponseData>(
                                    code = HttpStatusCode.TooManyRequests.value,
                                    message = "Concurrent login limit exceeded for this invitation code"
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    // 处理异常
                    call.respond(
                        HttpStatusCode.BadRequest,
                        standardResponse<EmptyResponseData>(
                            code = HttpStatusCode.BadRequest.value,
                            message = "Invalid request: ${e.message}"
                        )
                    )
                }
            }
        }
    }
}