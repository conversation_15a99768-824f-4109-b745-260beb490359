package com.dzhp.permit.routers.auth

import com.dzhp.permit.models.StandardResponseStruct
import com.dzhp.permit.services.auth.UserLogoutService
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable

/**
 * 用户登出请求
 */
@Serializable
data class UserLogoutRequest(
    val token: String? = null
)

/**
 * 用户登出路由
 *
 * - POST /api/user/logout : 用户登出
 */
fun Application.userLogoutRouter(userLogoutService: UserLogoutService) {
    routing {
        route("/api/user") {
            authenticate("user-llm-jwt") {
                post("/logout") {
                    val principal = call.principal<JWTPrincipal>()
                    val token = call.request.headers["Authorization"]?.substring(7)
                    
                    if (token != null) {
                        handleLogout(call, userLogoutService, token)
                    } else {
                        call.respond(
                            HttpStatusCode.BadRequest,
                            StandardResponseStruct(
                                code = HttpStatusCode.BadRequest.value,
                                message = "Token is required",
                                data = null
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 处理登出逻辑
 */
private suspend fun handleLogout(call: ApplicationCall, userLogoutService: UserLogoutService, token: String) {
    when (val result = userLogoutService.logout(token)) {
        UserLogoutService.LogoutResult.Success -> {
            call.respond(
                HttpStatusCode.OK,
                StandardResponseStruct(
                    code = HttpStatusCode.OK.value,
                    message = "Logout successful",
                    data = null
                )
            )
        }
        UserLogoutService.LogoutResult.InvalidToken -> {
            call.respond(
                HttpStatusCode.BadRequest,
                StandardResponseStruct(
                    code = HttpStatusCode.BadRequest.value,
                    message = "Invalid token",
                    data = null
                )
            )
        }
        UserLogoutService.LogoutResult.TokenExpired -> {
            call.respond(
                HttpStatusCode.Unauthorized,
                StandardResponseStruct(
                    code = HttpStatusCode.Unauthorized.value,
                    message = "Token expired",
                    data = null
                )
            )
        }
        UserLogoutService.LogoutResult.SessionNotFound -> {
            call.respond(
                HttpStatusCode.NotFound,
                StandardResponseStruct(
                    code = HttpStatusCode.NotFound.value,
                    message = "Session not found",
                    data = null
                )
            )
        }
    }
}
