package com.dzhp.permit

import com.dzhp.permit.services.WebSocketConnectionManager
import io.ktor.server.application.*
import io.ktor.server.websocket.*
import java.time.Duration

// 全局WebSocket连接管理器
private lateinit var webSocketConnectionManager: WebSocketConnectionManager

/**
 * 配置WebSocket
 * 设置WebSocket插件和初始化连接管理器
 */
fun Application.configureWebSockets() {
    // 安装WebSocket插件
    install(WebSockets) {
//        pingPeriod = Duration.ofSeconds(15)
//        timeout = Duration.ofSeconds(30)
        maxFrameSize = Long.MAX_VALUE
        masking = false
    }

    // 初始化WebSocket连接管理器
    webSocketConnectionManager = WebSocketConnectionManager(this)
}

/**
 * 获取WebSocket连接管理器的扩展函数
 * @return WebSocket连接管理器实例
 */
fun Application.getWebSocketConnectionManager(): WebSocketConnectionManager = webSocketConnectionManager
