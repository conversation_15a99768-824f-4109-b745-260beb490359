package com.dzhp.permit

import io.ktor.server.application.*

fun main(args: Array<String>) {
    io.ktor.server.netty.EngineMain.main(args)
}

fun Application.module() {
    configureHTTP()
    configureRedis()
    configureElasticSearch()
    configureMySQL()
    configureAliyunSMS() // 添加阿里云短信配置
    configureSecurity()
    configureSerialization()
    configureWebSockets() // 添加WebSocket配置
    configureRouting()
}

/**
 * 应用程序关闭时的清理工作
 */
fun Application.cleanup() {
    // 关闭ElasticSearch客户端连接
    closeElasticSearch()

    // 关闭阿里云短信客户端
    closeAliyunSMS()

    // 关闭MySQL连接可以在这里添加如果需要的话
    // 注意：Exposed的Database对象通常不需要显式关闭
}
