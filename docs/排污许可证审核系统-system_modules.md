# 系统模式与处理流程 system_modules.md

1. **核心架构模式**：
    -   分层架构
    -   事件驱动架构
    -   微服务架构（模块化设计）

2.  **主要处理流程**：

```mermaid
graph LR
    User[用户访问系统] --> InputCode[输入邀请码]
    InputCode --> ValidateCode{验证邀请码}
    ValidateCode -- 验证通过 --> DetermineUserType{确定用户类型}
    ValidateCode -- 验证失败 --> DisplayError[显示错误提示]
    DetermineUserType -- 县级行政机构用户 --> AccessPermitRegional[访问指定行政区域排污许可证]
    DetermineUserType -- 个人用户 --> CheckBalance[检查余额]
    CheckBalance -- 余额充足 --> AccessPermit[访问排污许可证（受并发限制）]
    CheckBalance -- 余额不足 --> DisplayRecharge[显示充值提示]
    AccessPermitRegional --> ConcurrentLimitRegional[并发限制（县级）]
    AccessPermit --> ConcurrentLimitPersonal[并发限制（个人）]
    ConcurrentLimitRegional --> SystemResources[系统资源]
    ConcurrentLimitPersonal --> SystemResources
    DisplayRecharge --> RechargeProcess[充值流程]
    RechargeProcess --> CheckBalance
```

3.  **模块详细设计**：

### 1. 邀请码验证模块
    -   功能：验证用户输入的邀请码，并根据验证结果重定向用户到相应的服务页面。
    -   处理流程：
        1.  接收用户输入的邀请码。
        2.  查询数据库，验证邀请码是否存在且有效。
        3.  如果验证通过，读取与邀请码关联的用户类型、权限等级和地域限制等信息。
        4.  根据用户类型和权限，将用户重定向到相应的服务页面。
        5.  如果验证失败，显示错误提示信息。
    -   AI代理：否
    -   特性：快速验证身份，权限控制入口。

### 2. 邀请码管理模块
    -   功能：生成、管理和查看邀请码的使用情况。
    -   处理流程：
        1.  管理员请求生成邀请码，可指定参数（用户类型、权限等级、地域限制、状态、过期时间、剩余金额、并发限制）。
        2.  系统生成唯一邀请码，并将其参数存储到数据库。
        3.  管理员可以启用、停用或删除邀请码。
        4.  系统记录邀请码的使用次数、金额消耗和时间分布等统计信息。
    -   AI代理：否
    -   特性：灵活的邀请码配置和管理，数据统计。

### 3. 用户权限分级模块
    -   功能：根据用户类型（县级行政机构用户或个人用户）和权限等级，控制用户对系统资源的访问。
    -   处理流程：
        1.  根据邀请码关联的用户类型，确定用户的访问权限。
        2.  对于县级行政机构用户，通过地域编码限制其只能访问指定行政区域内的排污许可证数据。
        3.  对于个人用户，检查其邀请码的剩余金额和并发限制。
    -   AI代理：否
    -   特性：精细化权限控制，支持不同用户类型。

### 4. 资源限制与计费模块
    -   功能：对系统资源的使用进行限制和计费。
    -   处理流程：
        1.  每个邀请码关联特定金额额度。
        2.  系统根据使用的LLM模型类型和输入输出token数量，实时计算费用。
        3.  当剩余金额不足时，系统提示充值并暂停服务。
        4.  定义每个邀请码的最大并发用户数，并实时监控使用状态。超出限制时提供等待队列或拒绝服务。
        5.  记录详细的计费信息，并提供账单生成功能。
    -   AI代理：否
    -   特性：资源用量控制，计费明细。