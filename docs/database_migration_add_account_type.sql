-- 数据库迁移脚本：为admin_key_info表添加account_type字段
-- 执行时间：2024年
-- 描述：为管理员密钥表添加账号性质字段，用于区分试用账号和正式账号

-- 1. 添加account_type字段
ALTER TABLE admin_key_info 
ADD COLUMN account_type VARCHAR(16) NOT NULL DEFAULT 'FORMAL' 
COMMENT '账号性质：TRIAL-试用账号，FORMAL-正式账号';

-- 2. 为现有数据设置默认值（所有现有记录设为正式账号）
UPDATE admin_key_info 
SET account_type = 'FORMAL' 
WHERE account_type IS NULL OR account_type = '';

-- 3. 添加索引（可选，如果需要按账号性质查询）
CREATE INDEX idx_admin_key_account_type ON admin_key_info(account_type);

-- 4. 验证迁移结果
SELECT 
    COUNT(*) as total_records,
    account_type,
    COUNT(*) as count_by_type
FROM admin_key_info 
GROUP BY account_type;

-- 5. 显示表结构确认字段已添加
DESCRIBE admin_key_info;
