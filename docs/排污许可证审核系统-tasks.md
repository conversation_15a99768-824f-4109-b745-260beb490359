# 项目任务清单 tasks.md

## 1. 规划与设计
- [x] 确定项目技术栈（Python/FastAPI, MySQL, Redis）
- [x] 完成数据库设计 (invitation_code表，用户权限相关表，计费日志表等)
- [x] 设计核心架构模式 (分层架构, 事件驱动架构, 微服务架构)
- [x] 绘制功能流程图 (邀请码验证, 用户类型判断, 资源访问, 充值流程等)
- [x] 设计API接口文档（邀请码验证，余额查询，充值等）
- [x] 确定项目编码规范和代码审查流程
- [x] 搭建开发环境 (包括数据库, Redis等)

## 2. 邀请码验证模块开发
- [ ] 创建邀请码验证API接口 (Python/FastAPI)
- [ ] 实现邀请码数据库查询功能
- [ ] 编写邀请码有效性验证逻辑 (过期时间, 状态, 并发限制等)
- [ ] 实现用户类型和权限信息读取逻辑
- [ ] 实现重定向功能 (根据用户类型重定向到不同服务页面)
- [ ] 实现验证失败错误提示功能

## 3. 邀请码管理模块开发
- [ ] 创建邀请码生成API接口 (支持单个和批量生成)
- [ ] 实现邀请码参数配置功能 (用户类型, 权限等级, 地域限制等)
- [ ] 完成唯一邀请码生成算法 (UUID或自定义算法)
- [ ] 实现邀请码数据存储功能 (MySQL数据库)
- [ ] 创建邀请码状态管理API接口 (启用, 停用, 删除)
- [ ] 设计并实现邀请码使用情况统计功能 (使用次数, 金额消耗, 时间分布等)
    - [ ] 创建日志表记录 invitation_code 使用情况

## 4. 用户权限分级模块开发
- [ ] 实现用户类型判断逻辑 (县级行政机构用户 or 个人用户)
- [ ] 针对县级行政机构用户，实现地域编码限制访问权限功能
    - [ ] 修改数据库查询逻辑，根据地域编码筛选数据
- [ ] 针对个人用户，实现余额和并发限制检查功能
- [ ] 创建余额查询API接口 (个人用户)
- [ ] 创建余额充值API接口 (个人用户)

## 5. 资源限制与计费模块开发
- [ ] 实现资源使用费用计算模块 (根据LLM模型类型和token数量)
- [ ] 创建LLM模型单价配置功能
- [ ] 实现余额不足提示功能 (暂停服务)
- [ ] 实现并发用户数监控功能 (使用Redis)
- [ ] 实现并发限制逻辑 (使用Redis原子性操作)
- [ ] 开发并发使用情况实时统计功能
- [ ] 创建计费记录存储功能 (记录每次API调用信息)
- [ ] 实现账单生成接口 (提供查询和下载功能)

## 6. 前端开发
- [ ] 设计简洁的邀请码输入页面 (HTML表单)
- [ ] 实现响应式设计 (支持不同设备访问)
- [ ] 调用邀请码验证API接口
- [ ] 实现错误提示显示功能
- [ ] 设计用户余额展示页面和充值页面 (个人用户)
- [ ] 构建后台管理界面 (邀请码管理功能)

## 7. 测试与部署
- [ ] 编写单元测试用例 (针对各个模块)
- [ ] 进行集成测试 (验证模块间协作)
- [ ] 进行性能测试 (模拟高并发场景)
- [ ] 部署系统到测试环境
- [ ] 进行用户验收测试 (UAT)
- [ ] 部署系统到生产环境
- [ ] 编写部署文档和用户手册
- [ ] 监控系统运行状态

## 8. 其他
- [ ] 配置日志系统，记录系统运行日志和用户操作日志
- [ ] 实施安全措施，防止SQL注入和XSS攻击
- [ ] 优化系统性能，例如使用缓存技术
- [ ] 定期备份数据库