# 排污许可证审核系统 - 访问控制需求规格说明
## 1. 系统概述
在现有排污许可证合规审核系统基础上，构建访问控制层，通过邀请码机制验证用户身份并分配相应的访问权限和资源限制。
## 2. 前置访问控制页面
### 2.1 界面要求
- 设计简洁的登录页面，主要包含邀请码输入框和验证按钮
- 提供适当的错误提示和操作指引
- 确保页面响应式设计，支持不同设备访问
### 2.2 功能流程
- 用户访问系统时首先展示邀请码验证页面
- 用户输入邀请码后系统进行验证
- 验证通过后根据邀请码关联的用户类型和权限重定向至相应服务页面
- 验证失败提供明确的错误提示
## 3. 邀请码管理系统
### 3.1 数据模型
- 邀请码(invitation_code): 唯一标识符
- 关联用户类型(user_type): 县级行政机构/个人用户
- 权限等级(permission_level): 定义访问权限范围
- 地域限制(region_constraint): 关联行政区域编码
- 使用状态(status): 有效/停用/过期
- 创建时间(created_at)和过期时间(expires_at)
- 剩余金额(remaining_balance): 邀请码可使用的剩余金额
- 并发限制(concurrent_limit): 允许同时使用该邀请码的最大用户数
- 当前并发数(current_concurrent): 当前使用该邀请码的用户数
### 3.2 管理功能
- 邀请码生成：支持批量或单个生成，可指定参数和限制条件
- 邀请码分配：记录分配对象和分配时间
- 邀请码状态管理：启用/停用/删除
- 使用情况统计：包括使用次数、金额消耗、时间分布等
## 4. 用户权限分级系统
### 4.1 县级行政机构用户
- 通过地域编码限制只能访问指定行政区域内的排污许可证
- 无使用次数限制，但需遵循并发限制规则
- 提供机构专属的使用统计和报告功能
### 4.2 个人用户
- 基于金额的使用限制，预付费模式
- 无地域限制但有金额和并发限制
- 提供余额查询和充值功能
## 5. 资源限制与计费系统
### 5.1 金额限制
- 每个邀请码关联特定金额额度
- 系统根据使用的LLM模型类型、输入输出token数量实时计算费用
- 当剩余金额不足时，系统提供明确提示并暂停服务
### 5.2 并发限制
- 定义每个邀请码的最大并发用户数
- 实时监控邀请码的使用状态，超出限制时提供等待队列或拒绝服务
- 提供并发使用情况的实时统计
### 5.3 计费规则
- 针对不同LLM模型定义不同的单价标准
- 分别计算输入token和输出token的费用
- 提供详细的计费记录和账单生成功能