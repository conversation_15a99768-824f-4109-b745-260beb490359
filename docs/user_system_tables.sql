# =======================================================
# 用户基本信息表
CREATE TABLE `user_info` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    
    # 基本信息
    `username` VARCHAR(64) NOT NULL COMMENT '用户名 | 用户唯一标识 | 可用于登录 | 创建后不可修改',
    `nickname` VARCHAR(64) NULL COMMENT '昵称 | 用户显示名称 | 可包含中文 | 用户可自行修改',
    `real_name` VARCHAR(64) NULL COMMENT '真实姓名 | 实名认证信息 | 需要身份验证',
    `avatar` VARCHAR(255) NULL COMMENT '头像URL | 用户头像图片地址 | 支持HTTPS链接',
    `gender` VARCHAR(16) NULL COMMENT '性别 | MALE:男 FEMALE:女 OTHER:其他 UNKNOWN:未知',
    `birthdate` VARCHAR(10) NULL COMMENT '出生日期 | ISO格式:YYYY-MM-DD | 用于年龄计算和生日提醒',
    `mobile` VARCHAR(20) NULL COMMENT '手机号 | 用于登录和验证 | 需短信验证后方可使用',
    `email` VARCHAR(128) NULL COMMENT '电子邮箱 | 用于通知和找回密码 | 需要邮件验证',
    `address` VARCHAR(255) NULL COMMENT '地址 | 用户联系地址 | 可用于物流配送等场景',
    `id_card_number` VARCHAR(32) NULL COMMENT '身份证号 | 用于实名认证 | 加密存储',
    `company_id` VARCHAR(64) NULL COMMENT '所属公司ID | 关联公司信息 | 用于企业用户管理',
    `department_id` VARCHAR(64) NULL COMMENT '所属部门ID | 关联部门信息 | 用于企业内部组织架构',
    `position` VARCHAR(64) NULL COMMENT '职位 | 用户在企业中的职位 | 用于权限分配和展示',
    
    # 用户状态和设置
    `status` VARCHAR(16) NOT NULL DEFAULT 'ACTIVE' COMMENT '用户状态 | ACTIVE:活跃 INACTIVE:未激活 SUSPENDED:暂停 LOCKED:锁定 BANNED:封禁 DELETED:已删除',
    `user_type` VARCHAR(32) NOT NULL COMMENT '用户类型 | COUNTY_ADMINISTRATION:县级行政机构 INDIVIDUAL:个人用户',
    `permission_level` VARCHAR(16) NOT NULL COMMENT '权限等级 | BASIC:基础 STANDARD:标准 ADVANCED:高级 ADMIN:管理员',
    `region_constraint` VARCHAR(32) NULL COMMENT '地域限制 | 行政区域编码 | 限制用户访问的地域范围',
    `preferred_language` VARCHAR(16) NULL COMMENT '首选语言 | 用户界面语言偏好 | ISO 639-1语言代码',
    `timezone` VARCHAR(32) NULL COMMENT '时区 | 用户所在时区 | IANA时区标识符',
    `notification_settings` TEXT NULL COMMENT '通知设置 | JSON格式 | 用户通知偏好配置',
    `security_level` VARCHAR(16) NOT NULL DEFAULT 'NORMAL' COMMENT '安全等级 | LOW:低 NORMAL:中 HIGH:高 VERY_HIGH:非常高',
    
    # 账户安全信息
    `password_updated_at` BIGINT NULL COMMENT '密码最后更新时间 | 毫秒级时间戳 | 用于密码过期提醒',
    `mfa_enabled` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否启用多因素认证 | 0:未启用 1:已启用 | 提高账号安全性',
    `login_fail_count` INT NOT NULL DEFAULT 0 COMMENT '连续登录失败次数 | 超过阈值将临时锁定账号 | 防止暴力破解',
    `last_login_ip` VARCHAR(64) NULL COMMENT '最后登录IP | 记录用户最近一次登录的IP地址 | 用于安全审计',
    `last_login_device` VARCHAR(255) NULL COMMENT '最后登录设备 | 记录用户最近一次登录的设备信息 | 用于安全审计',
    `last_login_at` BIGINT NULL COMMENT '最后登录时间 | 毫秒级时间戳 | 记录用户最近一次登录时间',
    `registration_ip` VARCHAR(64) NULL COMMENT '注册IP | 记录用户注册时的IP地址 | 用于安全审计',
    
    # 业务相关字段
    `user_level` INT NOT NULL DEFAULT 1 COMMENT '用户等级 | 用户成长体系等级 | 影响用户权益和服务',
    `vip_expire_at` BIGINT NULL COMMENT 'VIP过期时间 | 毫秒级时间戳 | 记录用户VIP服务到期时间',
    `invitation_code` VARCHAR(64) NULL COMMENT '注册使用的邀请码 | 记录用户注册时使用的邀请码 | 用于推广追踪',
    `invited_by` BIGINT NULL COMMENT '邀请人ID | 关联邀请人用户ID | 用于推广奖励和用户关系',
    `tags` TEXT NULL COMMENT '用户标签 | JSON数组格式 | 用于用户分类和个性化推荐',
    `remark` TEXT NULL COMMENT '备注 | 管理员对用户的备注信息 | 仅内部可见',
    
    # 审计字段
    `create_by` VARCHAR(64) NULL COMMENT '创建人 | 记录创建人的用户ID或系统标识',
    `update_by` VARCHAR(64) NULL COMMENT '更新人 | 记录最后修改人的用户ID或系统标识',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识 | 0:未删除 1:已删除 | 用于软删除功能',
    `created_at` BIGINT NOT NULL COMMENT '创建时间戳 | 记录创建时间 | 毫秒级时间戳',
    `updated_at` BIGINT NOT NULL COMMENT '更新时间戳 | 记录最后更新时间 | 毫秒级时间戳',
    `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号 | 用于并发控制 | 每次更新自增1',
    
    # 索引
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_mobile` (`mobile`),
    UNIQUE KEY `uk_email` (`email`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_department_id` (`department_id`),
    KEY `idx_user_type` (`user_type`),
    KEY `idx_status` (`status`),
    KEY `idx_user_level` (`user_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci 
COMMENT='用户基本信息表 | 存储用户的核心基本信息 | 用于用户管理和认证';

# =======================================================
# 用户身份认证表
CREATE TABLE `user_identity` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    
    # 身份信息
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID | 关联user_info表的id | 一个用户可以有多种登录方式',
    `identity_type` VARCHAR(16) NOT NULL COMMENT '身份类型 | WECHAT:微信 MOBILE:手机号 EMAIL:邮箱 USERNAME:用户名 INVITATION:邀请码等',
    `identifier` VARCHAR(128) NOT NULL COMMENT '身份标识 | 微信openid/手机号/邮箱/用户名/邀请码 | 在同一身份类型下唯一',
    `credential` VARCHAR(255) NULL COMMENT '凭证 | 密码哈希/token等 | 微信登录可为空 | 敏感信息加密存储',
    `is_verified` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已验证 | 0:未验证 1:已验证 | 标识该身份是否通过验证',
    `verified_at` BIGINT NULL COMMENT '验证时间 | 毫秒级时间戳 | 记录身份验证通过的时间',
    `verification_code` VARCHAR(64) NULL COMMENT '验证码 | 用于身份验证过程 | 验证完成后清空',
    `verification_code_expire_at` BIGINT NULL COMMENT '验证码过期时间 | 毫秒级时间戳 | 验证码的有效期',
    `last_used_at` BIGINT NULL COMMENT '最后使用时间 | 毫秒级时间戳 | 记录该身份最后一次用于登录的时间',
    `failed_attempts` INT NOT NULL DEFAULT 0 COMMENT '失败尝试次数 | 记录连续验证失败次数 | 超过阈值将锁定',
    `locked_until` BIGINT NULL COMMENT '锁定至时间 | 毫秒级时间戳 | 临时锁定到指定时间',
    `priority` INT NOT NULL DEFAULT 0 COMMENT '优先级 | 多种登录方式的排序优先级 | 数值越大优先级越高',
    `is_primary` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为主要身份 | 0:否 1:是 | 用户的主要身份标识',
    `extra_data` TEXT NULL COMMENT '额外数据 | JSON格式 | 存储与身份相关的额外信息',
    
    # 审计字段
    `create_by` VARCHAR(64) NULL COMMENT '创建人 | 记录创建人的用户ID或系统标识',
    `update_by` VARCHAR(64) NULL COMMENT '更新人 | 记录最后修改人的用户ID或系统标识',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识 | 0:未删除 1:已删除 | 用于软删除功能',
    `created_at` BIGINT NOT NULL COMMENT '创建时间戳 | 记录创建时间 | 毫秒级时间戳',
    `updated_at` BIGINT NOT NULL COMMENT '更新时间戳 | 记录最后更新时间 | 毫秒级时间戳',
    `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号 | 用于并发控制 | 每次更新自增1',
    
    # 索引
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_identity_type_identifier` (`identity_type`, `identifier`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_is_primary` (`is_primary`),
    KEY `idx_last_used_at` (`last_used_at`),
    CONSTRAINT `fk_user_identity_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_info` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci 
COMMENT='用户身份认证表 | 存储用户的多种登录身份信息 | 支持微信、手机号、邮箱等多种登录方式';

# =======================================================
# 用户多因素认证表
CREATE TABLE `user_mfa` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    
    # 基本信息
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID | 关联user_info表的id | 一个用户可以有多种MFA方式',
    `mfa_type` VARCHAR(16) NOT NULL COMMENT 'MFA类型 | TOTP:时间码 SMS:短信 EMAIL:邮件 BACKUP_CODE:备用码',
    `secret` VARCHAR(255) NOT NULL COMMENT '密钥 | MFA验证的密钥 | 加密存储',
    `backup_codes` TEXT NULL COMMENT '备用码 | JSON数组格式 | 用于紧急情况下的备用验证',
    `is_enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用 | 0:禁用 1:启用 | 控制该MFA方式是否生效',
    `last_used_at` BIGINT NULL COMMENT '最后使用时间 | 毫秒级时间戳 | 记录该MFA方式最后一次使用时间',
    
    # 审计字段
    `create_by` VARCHAR(64) NULL COMMENT '创建人 | 记录创建人的用户ID或系统标识',
    `update_by` VARCHAR(64) NULL COMMENT '更新人 | 记录最后修改人的用户ID或系统标识',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识 | 0:未删除 1:已删除 | 用于软删除功能',
    `created_at` BIGINT NOT NULL COMMENT '创建时间戳 | 记录创建时间 | 毫秒级时间戳',
    `updated_at` BIGINT NOT NULL COMMENT '更新时间戳 | 记录最后更新时间 | 毫秒级时间戳',
    
    # 索引
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id_mfa_type` (`user_id`, `mfa_type`),
    KEY `idx_user_id` (`user_id`),
    CONSTRAINT `fk_user_mfa_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_info` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci 
COMMENT='用户多因素认证表 | 存储用户的多因素认证信息 | 提高账号安全性';

# =======================================================
# 用户账户表
CREATE TABLE `user_account` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    
    # 基本信息
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID | 关联user_info表的id | 一个用户可以有多种账户类型',
    `account_type` VARCHAR(16) NOT NULL COMMENT '账户类型 | MAIN:主账户 GIFT:赠送账户 CREDIT:信用账户 POINT:积分账户 TOKEN:Token账户',
    `balance` DOUBLE NOT NULL DEFAULT 0.0 COMMENT '账户余额 | 当前账户可用余额 | 精确到小数点后2位',
    `frozen_amount` DOUBLE NOT NULL DEFAULT 0.0 COMMENT '冻结金额 | 当前被冻结的金额 | 不可用于消费',
    `total_recharge` DOUBLE NOT NULL DEFAULT 0.0 COMMENT '累计充值金额 | 历史总充值金额 | 用于统计分析',
    `total_consumption` DOUBLE NOT NULL DEFAULT 0.0 COMMENT '累计消费金额 | 历史总消费金额 | 用于统计分析',
    `total_gift` DOUBLE NOT NULL DEFAULT 0.0 COMMENT '累计赠送金额 | 历史总赠送金额 | 用于统计分析',
    `currency` VARCHAR(16) NOT NULL DEFAULT 'CNY' COMMENT '货币类型 | 账户货币单位 | ISO 4217货币代码',
    `status` VARCHAR(16) NOT NULL DEFAULT 'NORMAL' COMMENT '账户状态 | NORMAL:正常 FROZEN:冻结 SUSPENDED:暂停 CLOSED:关闭',
    `last_transaction_at` BIGINT NULL COMMENT '最后交易时间 | 毫秒级时间戳 | 记录最近一次交易时间',
    
    # 审计字段
    `create_by` VARCHAR(64) NULL COMMENT '创建人 | 记录创建人的用户ID或系统标识',
    `update_by` VARCHAR(64) NULL COMMENT '更新人 | 记录最后修改人的用户ID或系统标识',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识 | 0:未删除 1:已删除 | 用于软删除功能',
    `created_at` BIGINT NOT NULL COMMENT '创建时间戳 | 记录创建时间 | 毫秒级时间戳',
    `updated_at` BIGINT NOT NULL COMMENT '更新时间戳 | 记录最后更新时间 | 毫秒级时间戳',
    `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号 | 用于并发控制 | 每次更新自增1',
    
    # 索引
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id_account_type` (`user_id`, `account_type`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_user_account_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_info` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci 
COMMENT='用户账户表 | 存储用户的账户余额和财务信息 | 支持多种账户类型';

# =======================================================
# 交易记录表
CREATE TABLE `transaction_record` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    
    # 基本信息
    `transaction_no` VARCHAR(64) NOT NULL COMMENT '交易流水号 | 全局唯一标识符 | 用于交易追踪和对账',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID | 关联user_info表的id | 交易所属用户',
    `account_id` BIGINT UNSIGNED NOT NULL COMMENT '账户ID | 关联user_account表的id | 交易所属账户',
    `transaction_type` VARCHAR(32) NOT NULL COMMENT '交易类型 | RECHARGE:充值 CONSUMPTION:消费 REFUND:退款 GIFT:赠送 等',
    `amount` DOUBLE NOT NULL COMMENT '交易金额 | 正数表示收入，负数表示支出 | 精确到小数点后2位',
    `balance` DOUBLE NOT NULL COMMENT '交易后余额 | 交易完成后的账户余额 | 用于对账',
    `related_transaction_id` BIGINT NULL COMMENT '关联交易ID | 关联其他交易记录 | 如退款关联原消费交易',
    `status` VARCHAR(16) NOT NULL COMMENT '交易状态 | PENDING:处理中 SUCCESS:成功 FAILED:失败 CANCELLED:已取消 REFUNDED:已退款',
    `description` VARCHAR(255) NULL COMMENT '交易描述 | 交易的文字说明 | 用于展示给用户',
    `metadata` TEXT NULL COMMENT '元数据 | JSON格式 | 存储交易相关的额外信息',
    `operator_id` VARCHAR(64) NULL COMMENT '操作人ID | 执行交易操作的用户ID | 用于审计',
    `operator_name` VARCHAR(64) NULL COMMENT '操作人名称 | 执行交易操作的用户名称 | 用于展示',
    `operator_ip` VARCHAR(64) NULL COMMENT '操作人IP | 执行交易操作的IP地址 | 用于安全审计',
    
    # 审计字段
    `create_by` VARCHAR(64) NULL COMMENT '创建人 | 记录创建人的用户ID或系统标识',
    `update_by` VARCHAR(64) NULL COMMENT '更新人 | 记录最后修改人的用户ID或系统标识',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识 | 0:未删除 1:已删除 | 用于软删除功能',
    `created_at` BIGINT NOT NULL COMMENT '创建时间戳 | 记录创建时间 | 毫秒级时间戳',
    `updated_at` BIGINT NOT NULL COMMENT '更新时间戳 | 记录最后更新时间 | 毫秒级时间戳',
    
    # 索引
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_transaction_no` (`transaction_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_account_id` (`account_id`),
    KEY `idx_transaction_type` (`transaction_type`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_transaction_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_info` (`id`),
    CONSTRAINT `fk_transaction_account_id` FOREIGN KEY (`account_id`) REFERENCES `user_account` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci 
COMMENT='交易记录表 | 存储用户账户的交易记录 | 用于财务对账和用户查询';
