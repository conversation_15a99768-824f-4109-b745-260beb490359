CREATE TABLE `invitation_code_info` (
    `id`                BIGINT           NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
    `code`              VARCHAR(64)      NOT NULL COMMENT '邀请码唯一标识符',
    `company_id`        VARCHAR(64)      NOT NULL COMMENT '所属公司ID，可为空',
    `user_type`         VARCHAR(32)      NOT NULL COMMENT '用户类型：COUNTY_ADMINISTRATION / INDIVIDUAL',
    `permission_level`  VARCHAR(16)      NOT NULL COMMENT '权限等级：BASIC / STANDARD / ADVANCED / ADMIN',
    `region_constraint` VARCHAR(32)      NULL COMMENT '地域限制，行政区划编码，可为空',
    `status`            VARCHAR(16)      NOT NULL COMMENT '状态：ACTIVE / DISABLED / EXPIRED',
    `remaining_balance` DOUBLE           NOT NULL COMMENT '剩余可用余额（TOKEN）',
    `concurrent_limit`  INT              NOT NULL COMMENT '并发使用上限',
    `create_by`         VARCHAR(64)      NULL COMMENT '创建人，可为空',
    `update_by`         VARCHAR(64)      NULL COMMENT '更新人，可为空',
    `deleted`           TINYINT(1)       NOT NULL DEFAULT 0 COMMENT '软删除标记：0-未删除 1-已删除',
    `created_at`        BIGINT           NOT NULL COMMENT '创建时间戳（毫秒）',
    `expires_at`        BIGINT           NOT NULL COMMENT '过期时间戳（毫秒）',
    `update_at`         BIGINT           NOT NULL COMMENT '更新时间戳（毫秒）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_invitation_code` (`code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
    COMMENT = '邀请码表';


# =======================================================
# LLM 用量统计表
CREATE TABLE `llm_usage_log` (
     `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一 ID',
     `model_name` VARCHAR(64)  NOT NULL COMMENT 'LLM 模型名称',
     `prompt_tokens` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '提示词 token 数量',
     `completion_tokens` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '补全 token 数量',
     `total_tokens` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总 token 数量',
     `timestamp` BIGINT UNSIGNED NOT NULL COMMENT '时间戳（毫秒）',
     `call_by` VARCHAR(255) NULL COMMENT '调用来源（可为空）',
     `user_id` VARCHAR(64) NULL COMMENT '用户ID（可为空）',
     `request_id` VARCHAR(64) NULL COMMENT '请求ID（可为空）',
     PRIMARY KEY (`id`),
     KEY `idx_model_time` (`model_name`, `timestamp`),
     KEY `idx_user_id` (`user_id`),
     KEY `idx_request_id` (`request_id`)
) ENGINE=InnoDB
  DEFAULT CHARSET=utf8mb4
    COMMENT='LLM 使用量日志';