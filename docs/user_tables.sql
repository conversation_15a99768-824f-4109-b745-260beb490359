# =======================================================
# 用户基本信息表
CREATE TABLE `user_info` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    
    # 基本信息
    `username` VARCHAR(64) NOT NULL COMMENT '用户名 | 用户唯一标识 | 可用于登录 | 创建后不可修改',
    `nickname` VARCHAR(64) NULL COMMENT '昵称 | 用户显示名称 | 可包含中文 | 用户可自行修改',
    `avatar` VARCHAR(255) NULL COMMENT '头像URL | 用户头像图片地址 | 支持HTTPS链接',
    `mobile` VARCHAR(20) NULL COMMENT '手机号 | 用于登录和验证 | 需短信验证后方可使用',
    `email` VARCHAR(128) NULL COMMENT '电子邮箱 | 用于通知和找回密码 | 需要邮件验证',
    
    # 账户信息
    `available_balance` DOUBLE NOT NULL DEFAULT 0.0 COMMENT '可用余额 | 用户当前可用的总余额 | 包含实际充值和赠送余额',
    `actual_balance` DOUBLE NOT NULL DEFAULT 0.0 COMMENT '实际充值余额 | 用户实际充值的余额 | 不包含赠送余额',
    `gift_balance` DOUBLE NOT NULL DEFAULT 0.0 COMMENT '赠送余额 | 系统赠送的余额 | 优先消耗赠送余额',
    
    # 用户状态和权限
    `status` VARCHAR(16) NOT NULL DEFAULT 'ACTIVE' COMMENT '用户状态 | ACTIVE:活跃 INACTIVE:未激活 SUSPENDED:暂停 BANNED:封禁',
    `user_type` VARCHAR(32) NOT NULL COMMENT '用户类型 | COUNTY_ADMINISTRATION:县级行政机构 INDIVIDUAL:个人用户',
    `permission_level` VARCHAR(16) NOT NULL COMMENT '权限等级 | BASIC:基础 STANDARD:标准 ADVANCED:高级 ADMIN:管理员',
    `region_constraint` VARCHAR(32) NULL COMMENT '地域限制 | 行政区域编码 | 限制用户访问的地域范围',
    
    # 审计字段
    `create_by` VARCHAR(64) NULL COMMENT '创建人 | 记录创建人的用户ID或系统标识',
    `update_by` VARCHAR(64) NULL COMMENT '更新人 | 记录最后修改人的用户ID或系统标识',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识 | 0:未删除 1:已删除 | 用于软删除功能',
    `created_at` BIGINT NOT NULL COMMENT '创建时间戳 | 记录创建时间 | 毫秒级时间戳',
    `updated_at` BIGINT NOT NULL COMMENT '更新时间戳 | 记录最后更新时间 | 毫秒级时间戳',
    `last_login_at` BIGINT NULL COMMENT '最后登录时间 | 记录用户最近一次成功登录的时间 | 毫秒级时间戳',
    
    # 索引
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_mobile` (`mobile`),
    UNIQUE KEY `uk_email` (`email`),
    KEY `idx_user_type` (`user_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci 
COMMENT='用户基本信息表 | 存储用户的基本信息和账户信息 | 用于用户管理和认证';

# =======================================================
# 用户身份认证表
CREATE TABLE `user_identity` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    
    # 身份信息
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID | 关联user_info表的id | 一个用户可以有多种登录方式',
    `identity_type` VARCHAR(16) NOT NULL COMMENT '身份类型 | WECHAT:微信 MOBILE:手机号 EMAIL:邮箱 USERNAME:用户名 INVITATION:邀请码',
    `identifier` VARCHAR(128) NOT NULL COMMENT '身份标识 | 微信openid/手机号/邮箱/用户名/邀请码 | 在同一身份类型下唯一',
    `credential` VARCHAR(255) NULL COMMENT '凭证 | 密码哈希/token等 | 微信登录可为空 | 敏感信息加密存储',
    `is_verified` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已验证 | 0:未验证 1:已验证 | 标识该身份是否通过验证',
    `extra_data` TEXT NULL COMMENT '额外数据 | JSON格式存储 | 如微信unionId、用户设备信息等',
    
    # 审计字段
    `create_by` VARCHAR(64) NULL COMMENT '创建人 | 记录创建人的用户ID或系统标识',
    `update_by` VARCHAR(64) NULL COMMENT '更新人 | 记录最后修改人的用户ID或系统标识',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识 | 0:未删除 1:已删除 | 用于软删除功能',
    `created_at` BIGINT NOT NULL COMMENT '创建时间戳 | 记录创建时间 | 毫秒级时间戳',
    `updated_at` BIGINT NOT NULL COMMENT '更新时间戳 | 记录最后更新时间 | 毫秒级时间戳',
    
    # 索引
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_identity_type_identifier` (`identity_type`, `identifier`),
    KEY `idx_user_id` (`user_id`),
    CONSTRAINT `fk_user_identity_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_info` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci 
COMMENT='用户身份认证表 | 存储用户的多种登录身份信息 | 支持微信、手机号、邮箱等多种登录方式';
